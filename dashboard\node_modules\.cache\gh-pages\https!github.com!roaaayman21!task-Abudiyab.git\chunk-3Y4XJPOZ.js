import{A as J,Aa as nn,B as ge,Ba as ke,C as se,Ca as _e,Da as B,E as Yt,Ea as rn,F as me,Ga as Kr,<PERSON> as Ur,Ha as sn,<PERSON>a as Xr,<PERSON> as jr,Ja as ce,L as oe,La as on,Lb as ci,M as Y,Ma as ye,Na as Re,O as Qt,Oa as Zr,<PERSON> as kr,<PERSON> as Jr,Pb as gt,Qa as Yr,Qb as ui,R as _r,<PERSON> as Qr,Rb as cn,S as ft,Sa as ei,Sb as mt,Ta as ti,Tb as un,Ua as ni,V as Lr,Va as ue,Vb as ln,W as L,Wb as dn,X as Fr,Xa as Le,Xb as li,Y as D,Z as $r,_ as C,_b as vt,a as f,aa as R,ab as ri,ac as hn,b as M,bc as di,ca as zr,cb as ii,cc as hi,d as Ht,da as p,dc as U,e as Ar,ea as en,eb as an,ec as yt,f as Mr,fa as m,fc as Rt,g as ie,ga as h,h as Kt,ha as Br,hb as si,i as Xt,ia as pt,ib as oi,j as q,ja as Ue,k as P,ka as Vr,l as Dr,lc as fi,m as Z,ma as qr,n as Or,na as ae,nc as wt,o as x,oa as Gr,oc as St,p as g,pa as ve,q as xe,qa as $,r as Pr,ra as Wr,rb as ai,s as Nr,sa as Hr,t as E,u as Zt,v as _,w as xr,x as Jt,xa as je,za as tn}from"./chunk-5P3CUSN4.js";var $e=class{},Et=class{},G=class t{constructor(n){this.normalizedNames=new Map,this.lazyUpdate=null,n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(e=>{let r=e.indexOf(":");if(r>0){let i=e.slice(0,r),s=i.toLowerCase(),o=e.slice(r+1).trim();this.maybeSetNormalizedName(i,s),this.headers.has(s)?this.headers.get(s).push(o):this.headers.set(s,[o])}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((e,r)=>{this.setHeaderEntries(r,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([e,r])=>{this.setHeaderEntries(e,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let e=this.headers.get(n.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,e){return this.clone({name:n,value:e,op:"a"})}set(n,e){return this.clone({name:n,value:e,op:"s"})}delete(n,e){return this.clone({name:n,value:e,op:"d"})}maybeSetNormalizedName(n,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,n)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(e=>{this.headers.set(e,n.headers.get(e)),this.normalizedNames.set(e,n.normalizedNames.get(e))})}clone(n){let e=new t;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([n]),e}applyUpdate(n){let e=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,e);let i=(n.op==="a"?this.headers.get(e):void 0)||[];i.push(...r),this.headers.set(e,i);break;case"d":let s=n.value;if(!s)this.headers.delete(e),this.normalizedNames.delete(e);else{let o=this.headers.get(e);if(!o)return;o=o.filter(c=>s.indexOf(c)===-1),o.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,o)}break}}setHeaderEntries(n,e){let r=(Array.isArray(e)?e:[e]).map(s=>s.toString()),i=n.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(n,i)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>n(this.normalizedNames.get(e),this.headers.get(e)))}};var pn=class{encodeKey(n){return pi(n)}encodeValue(n){return pi(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function zs(t,n){let e=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(i=>{let s=i.indexOf("="),[o,c]=s==-1?[n.decodeKey(i),""]:[n.decodeKey(i.slice(0,s)),n.decodeValue(i.slice(s+1))],a=e.get(o)||[];a.push(c),e.set(o,a)}),e}var Bs=/%(\d[a-f0-9])/gi,Vs={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function pi(t){return encodeURIComponent(t).replace(Bs,(n,e)=>Vs[e]??n)}function bt(t){return`${t}`}var ee=class t{constructor(n={}){if(this.updates=null,this.cloneFrom=null,this.encoder=n.encoder||new pn,n.fromString){if(n.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=zs(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(e=>{let r=n.fromObject[e],i=Array.isArray(r)?r.map(bt):[bt(r)];this.map.set(e,i)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let e=this.map.get(n);return e?e[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,e){return this.clone({param:n,value:e,op:"a"})}appendAll(n){let e=[];return Object.keys(n).forEach(r=>{let i=n[r];Array.isArray(i)?i.forEach(s=>{e.push({param:r,value:s,op:"a"})}):e.push({param:r,value:i,op:"a"})}),this.clone(e)}set(n,e){return this.clone({param:n,value:e,op:"s"})}delete(n,e){return this.clone({param:n,value:e,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let e=this.encoder.encodeKey(n);return this.map.get(n).map(r=>e+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let e=new t({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(n),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let e=(n.op==="a"?this.map.get(n.param):void 0)||[];e.push(bt(n.value)),this.map.set(n.param,e);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],i=r.indexOf(bt(n.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var gn=class{constructor(){this.map=new Map}set(n,e){return this.map.set(n,e),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function qs(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function gi(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function mi(t){return typeof Blob<"u"&&t instanceof Blob}function vi(t){return typeof FormData<"u"&&t instanceof FormData}function Gs(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var Fe=class t{constructor(n,e,r,i){this.url=e,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=n.toUpperCase();let s;if(qs(this.method)||i?(this.body=r!==void 0?r:null,s=i):s=r,s&&(this.reportProgress=!!s.reportProgress,this.withCredentials=!!s.withCredentials,s.responseType&&(this.responseType=s.responseType),s.headers&&(this.headers=s.headers),s.context&&(this.context=s.context),s.params&&(this.params=s.params),this.transferCache=s.transferCache),this.headers??=new G,this.context??=new gn,!this.params)this.params=new ee,this.urlWithParams=e;else{let o=this.params.toString();if(o.length===0)this.urlWithParams=e;else{let c=e.indexOf("?"),a=c===-1?"?":c<e.length-1?"&":"";this.urlWithParams=e+a+o}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||gi(this.body)||mi(this.body)||vi(this.body)||Gs(this.body)?this.body:this.body instanceof ee?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||vi(this.body)?null:mi(this.body)?this.body.type||null:gi(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof ee?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(n={}){let e=n.method||this.method,r=n.url||this.url,i=n.responseType||this.responseType,s=n.transferCache??this.transferCache,o=n.body!==void 0?n.body:this.body,c=n.withCredentials??this.withCredentials,a=n.reportProgress??this.reportProgress,u=n.headers||this.headers,l=n.params||this.params,d=n.context??this.context;return n.setHeaders!==void 0&&(u=Object.keys(n.setHeaders).reduce((w,S)=>w.set(S,n.setHeaders[S]),u)),n.setParams&&(l=Object.keys(n.setParams).reduce((w,S)=>w.set(S,n.setParams[S]),l)),new t(e,r,o,{params:l,headers:u,context:d,reportProgress:a,responseType:i,withCredentials:c,transferCache:s})}},te=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(te||{}),ze=class{constructor(n,e=200,r="OK"){this.headers=n.headers||new G,this.status=n.status!==void 0?n.status:e,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},Tt=class t extends ze{constructor(n={}){super(n),this.type=te.ResponseHeader}clone(n={}){return new t({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Be=class t extends ze{constructor(n={}){super(n),this.type=te.Response,this.body=n.body!==void 0?n.body:null}clone(n={}){return new t({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Q=class extends ze{constructor(n){super(n,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},bi=200,Ws=204;function fn(t,n){return{body:n,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var Hs=(()=>{class t{constructor(e){this.handler=e}request(e,r,i={}){let s;if(e instanceof Fe)s=e;else{let a;i.headers instanceof G?a=i.headers:a=new G(i.headers);let u;i.params&&(i.params instanceof ee?u=i.params:u=new ee({fromObject:i.params})),s=new Fe(e,r,i.body!==void 0?i.body:null,{headers:a,context:i.context,params:u,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let o=g(s).pipe(se(a=>this.handler.handle(a)));if(e instanceof Fe||i.observe==="events")return o;let c=o.pipe(J(a=>a instanceof Be));switch(i.observe||"body"){case"body":switch(s.responseType){case"arraybuffer":return c.pipe(E(a=>{if(a.body!==null&&!(a.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return a.body}));case"blob":return c.pipe(E(a=>{if(a.body!==null&&!(a.body instanceof Blob))throw new Error("Response is not a Blob.");return a.body}));case"text":return c.pipe(E(a=>{if(a.body!==null&&typeof a.body!="string")throw new Error("Response is not a string.");return a.body}));case"json":default:return c.pipe(E(a=>a.body))}case"response":return c;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(e,r={}){return this.request("DELETE",e,r)}get(e,r={}){return this.request("GET",e,r)}head(e,r={}){return this.request("HEAD",e,r)}jsonp(e,r){return this.request("JSONP",e,{params:new ee().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,r={}){return this.request("OPTIONS",e,r)}patch(e,r,i={}){return this.request("PATCH",e,fn(i,r))}post(e,r,i={}){return this.request("POST",e,fn(i,r))}put(e,r,i={}){return this.request("PUT",e,fn(i,r))}static{this.\u0275fac=function(r){return new(r||t)(m($e))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),Ks=/^\)\]\}',?\n/,Xs="X-Request-URL";function yi(t){if(t.url)return t.url;let n=Xs.toLocaleLowerCase();return t.headers.get(n)}var Zs=(()=>{class t{constructor(){this.fetchImpl=h(mn,{optional:!0})?.fetch??((...e)=>globalThis.fetch(...e)),this.ngZone=h(B)}handle(e){return new ie(r=>{let i=new AbortController;return this.doRequest(e,i.signal,r).then(vn,s=>r.error(new Q({error:s}))),()=>i.abort()})}doRequest(e,r,i){return Ht(this,null,function*(){let s=this.createRequestInit(e),o;try{let S=this.ngZone.runOutsideAngular(()=>this.fetchImpl(e.urlWithParams,f({signal:r},s)));Js(S),i.next({type:te.Sent}),o=yield S}catch(S){i.error(new Q({error:S,status:S.status??0,statusText:S.statusText,url:e.urlWithParams,headers:S.headers}));return}let c=new G(o.headers),a=o.statusText,u=yi(o)??e.urlWithParams,l=o.status,d=null;if(e.reportProgress&&i.next(new Tt({headers:c,status:l,statusText:a,url:u})),o.body){let S=o.headers.get("content-length"),I=[],b=o.body.getReader(),y=0,N,X,A=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>Ht(this,null,function*(){for(;;){let{done:re,value:Ne}=yield b.read();if(re)break;if(I.push(Ne),y+=Ne.length,e.reportProgress){X=e.responseType==="text"?(X??"")+(N??=new TextDecoder).decode(Ne,{stream:!0}):void 0;let Ir=()=>i.next({type:te.DownloadProgress,total:S?+S:void 0,loaded:y,partialText:X});A?A.run(Ir):Ir()}}}));let Pe=this.concatChunks(I,y);try{let re=o.headers.get("Content-Type")??"";d=this.parseBody(e,Pe,re)}catch(re){i.error(new Q({error:re,headers:new G(o.headers),status:o.status,statusText:o.statusText,url:yi(o)??e.urlWithParams}));return}}l===0&&(l=d?bi:0),l>=200&&l<300?(i.next(new Be({body:d,headers:c,status:l,statusText:a,url:u})),i.complete()):i.error(new Q({error:d,headers:c,status:l,statusText:a,url:u}))})}parseBody(e,r,i){switch(e.responseType){case"json":let s=new TextDecoder().decode(r).replace(Ks,"");return s===""?null:JSON.parse(s);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(e){let r={},i=e.withCredentials?"include":void 0;if(e.headers.forEach((s,o)=>r[s]=o.join(",")),e.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!e.headers.has("Content-Type")){let s=e.detectContentTypeHeader();s!==null&&(r["Content-Type"]=s)}return{body:e.serializeBody(),method:e.method,headers:r,credentials:i}}concatChunks(e,r){let i=new Uint8Array(r),s=0;for(let o of e)i.set(o,s),s+=o.length;return i}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),mn=class{};function vn(){}function Js(t){t.then(vn,vn)}function Ei(t,n){return n(t)}function Ys(t,n){return(e,r)=>n.intercept(e,{handle:i=>t(i,r)})}function Qs(t,n,e){return(r,i)=>$(e,()=>n(r,s=>t(s,i)))}var eo=new p(""),Sn=new p(""),to=new p(""),Ti=new p("",{providedIn:"root",factory:()=>!0});function no(){let t=null;return(n,e)=>{t===null&&(t=(h(eo,{optional:!0})??[]).reduceRight(Ys,Ei));let r=h(ke);if(h(Ti)){let s=r.add();return t(n,e).pipe(oe(()=>r.remove(s)))}else return t(n,e)}}var Ri=(()=>{class t extends $e{constructor(e,r){super(),this.backend=e,this.injector=r,this.chain=null,this.pendingTasks=h(ke),this.contributeToStability=h(Ti)}handle(e){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Sn),...this.injector.get(to,[])]));this.chain=r.reduceRight((i,s)=>Qs(i,s,this.injector),Ei)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(e,i=>this.backend.handle(i)).pipe(oe(()=>this.pendingTasks.remove(r)))}else return this.chain(e,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||t)(m(Et),m(ve))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})();var ro=/^\)\]\}',?\n/;function io(t){return"responseURL"in t&&t.responseURL?t.responseURL:/^X-Request-URL:/m.test(t.getAllResponseHeaders())?t.getResponseHeader("X-Request-URL"):null}var wi=(()=>{class t{constructor(e){this.xhrFactory=e}handle(e){if(e.method==="JSONP")throw new C(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?x(r.\u0275loadImpl()):g(null)).pipe(L(()=>new ie(s=>{let o=r.build();if(o.open(e.method,e.urlWithParams),e.withCredentials&&(o.withCredentials=!0),e.headers.forEach((b,y)=>o.setRequestHeader(b,y.join(","))),e.headers.has("Accept")||o.setRequestHeader("Accept","application/json, text/plain, */*"),!e.headers.has("Content-Type")){let b=e.detectContentTypeHeader();b!==null&&o.setRequestHeader("Content-Type",b)}if(e.responseType){let b=e.responseType.toLowerCase();o.responseType=b!=="json"?b:"text"}let c=e.serializeBody(),a=null,u=()=>{if(a!==null)return a;let b=o.statusText||"OK",y=new G(o.getAllResponseHeaders()),N=io(o)||e.url;return a=new Tt({headers:y,status:o.status,statusText:b,url:N}),a},l=()=>{let{headers:b,status:y,statusText:N,url:X}=u(),A=null;y!==Ws&&(A=typeof o.response>"u"?o.responseText:o.response),y===0&&(y=A?bi:0);let Pe=y>=200&&y<300;if(e.responseType==="json"&&typeof A=="string"){let re=A;A=A.replace(ro,"");try{A=A!==""?JSON.parse(A):null}catch(Ne){A=re,Pe&&(Pe=!1,A={error:Ne,text:A})}}Pe?(s.next(new Be({body:A,headers:b,status:y,statusText:N,url:X||void 0})),s.complete()):s.error(new Q({error:A,headers:b,status:y,statusText:N,url:X||void 0}))},d=b=>{let{url:y}=u(),N=new Q({error:b,status:o.status||0,statusText:o.statusText||"Unknown Error",url:y||void 0});s.error(N)},w=!1,S=b=>{w||(s.next(u()),w=!0);let y={type:te.DownloadProgress,loaded:b.loaded};b.lengthComputable&&(y.total=b.total),e.responseType==="text"&&o.responseText&&(y.partialText=o.responseText),s.next(y)},I=b=>{let y={type:te.UploadProgress,loaded:b.loaded};b.lengthComputable&&(y.total=b.total),s.next(y)};return o.addEventListener("load",l),o.addEventListener("error",d),o.addEventListener("timeout",d),o.addEventListener("abort",d),e.reportProgress&&(o.addEventListener("progress",S),c!==null&&o.upload&&o.upload.addEventListener("progress",I)),o.send(c),s.next({type:te.Sent}),()=>{o.removeEventListener("error",d),o.removeEventListener("abort",d),o.removeEventListener("load",l),o.removeEventListener("timeout",d),e.reportProgress&&(o.removeEventListener("progress",S),c!==null&&o.upload&&o.upload.removeEventListener("progress",I)),o.readyState!==o.DONE&&o.abort()}})))}static{this.\u0275fac=function(r){return new(r||t)(m(St))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),Ci=new p(""),so="XSRF-TOKEN",oo=new p("",{providedIn:"root",factory:()=>so}),ao="X-XSRF-TOKEN",co=new p("",{providedIn:"root",factory:()=>ao}),Ct=class{},uo=(()=>{class t{constructor(e,r,i){this.doc=e,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=Rt(e,this.cookieName),this.lastCookieString=e),this.lastToken}static{this.\u0275fac=function(r){return new(r||t)(m(U),m(ce),m(oo))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})();function lo(t,n){let e=t.url.toLowerCase();if(!h(Ci)||t.method==="GET"||t.method==="HEAD"||e.startsWith("http://")||e.startsWith("https://"))return n(t);let r=h(Ct).getToken(),i=h(co);return r!=null&&!t.headers.has(i)&&(t=t.clone({headers:t.headers.set(i,r)})),n(t)}var Ii=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(Ii||{});function ho(t,n){return{\u0275kind:t,\u0275providers:n}}function Uu(...t){let n=[Hs,wi,Ri,{provide:$e,useExisting:Ri},{provide:Et,useFactory:()=>h(Zs,{optional:!0})??h(wi)},{provide:Sn,useValue:lo,multi:!0},{provide:Ci,useValue:!0},{provide:Ct,useClass:uo}];for(let e of t)n.push(...e.\u0275providers);return ae(n)}var Si=new p("");function ju(){return ho(Ii.LegacyInterceptors,[{provide:Si,useFactory:no},{provide:Sn,useExisting:Si,multi:!0}])}var Tn=class extends hi{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Cn=class t extends Tn{static makeCurrent(){di(new t)}onAndCancel(n,e,r){return n.addEventListener(e,r),()=>{n.removeEventListener(e,r)}}dispatchEvent(n,e){n.dispatchEvent(e)}remove(n){n.remove()}createElement(n,e){return e=e||this.getDefaultDocument(),e.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,e){return e==="window"?window:e==="document"?n:e==="body"?n.body:null}getBaseHref(n){let e=go();return e==null?null:mo(e)}resetBaseElement(){Ve=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Rt(document.cookie,n)}},Ve=null;function go(){return Ve=Ve||document.querySelector("base"),Ve?Ve.getAttribute("href"):null}function mo(t){return new URL(t,document.baseURI).pathname}var vo=(()=>{class t{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),In=new p(""),Oi=(()=>{class t{constructor(e,r){this._zone=r,this._eventNameToPlugin=new Map,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,r,i){return this._findPluginFor(r).addEventListener(e,r,i)}getZone(){return this._zone}_findPluginFor(e){let r=this._eventNameToPlugin.get(e);if(r)return r;if(r=this._plugins.find(s=>s.supports(e)),!r)throw new C(5101,!1);return this._eventNameToPlugin.set(e,r),r}static{this.\u0275fac=function(r){return new(r||t)(m(In),m(B))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),It=class{constructor(n){this._doc=n}},bn="ng-app-id",Pi=(()=>{class t{constructor(e,r,i,s={}){this.doc=e,this.appId=r,this.nonce=i,this.platformId=s,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=wt(s),this.resetHostNodes()}addStyles(e){for(let r of e)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(e){for(let r of e)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let e=this.styleNodesInDOM;e&&(e.forEach(r=>r.remove()),e.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(e){this.hostNodes.add(e);for(let r of this.getAllStyles())this.addStyleToHost(e,r)}removeHost(e){this.hostNodes.delete(e)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(e){for(let r of this.hostNodes)this.addStyleToHost(r,e)}onStyleRemoved(e){let r=this.styleRef;r.get(e)?.elements?.forEach(i=>i.remove()),r.delete(e)}collectServerRenderedStyles(){let e=this.doc.head?.querySelectorAll(`style[${bn}="${this.appId}"]`);if(e?.length){let r=new Map;return e.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(e,r){let i=this.styleRef;if(i.has(e)){let s=i.get(e);return s.usage+=r,s.usage}return i.set(e,{usage:r,elements:[]}),r}getStyleElement(e,r){let i=this.styleNodesInDOM,s=i?.get(r);if(s?.parentNode===e)return i.delete(r),s.removeAttribute(bn),s;{let o=this.doc.createElement("style");return this.nonce&&o.setAttribute("nonce",this.nonce),o.textContent=r,this.platformIsServer&&o.setAttribute(bn,this.appId),e.appendChild(o),o}}addStyleToHost(e,r){let i=this.getStyleElement(e,r),s=this.styleRef,o=s.get(r)?.elements;o?o.push(i):s.set(r,{elements:[i],usage:1})}resetHostNodes(){let e=this.hostNodes;e.clear(),e.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||t)(m(U),m(sn),m(on,8),m(ce))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),En={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Mn=/%COMP%/g,Ni="%COMP%",yo=`_nghost-${Ni}`,Ro=`_ngcontent-${Ni}`,wo=!0,So=new p("",{providedIn:"root",factory:()=>wo});function bo(t){return Ro.replace(Mn,t)}function Eo(t){return yo.replace(Mn,t)}function xi(t,n){return n.map(e=>e.replace(Mn,t))}var Ai=(()=>{class t{constructor(e,r,i,s,o,c,a,u=null){this.eventManager=e,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=o,this.platformId=c,this.ngZone=a,this.nonce=u,this.rendererByCompId=new Map,this.platformIsServer=wt(c),this.defaultRenderer=new qe(e,o,a,this.platformIsServer)}createRenderer(e,r){if(!e||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ue.ShadowDom&&(r=M(f({},r),{encapsulation:Ue.Emulated}));let i=this.getOrCreateRenderer(e,r);return i instanceof At?i.applyToHost(e):i instanceof Ge&&i.applyStyles(),i}getOrCreateRenderer(e,r){let i=this.rendererByCompId,s=i.get(r.id);if(!s){let o=this.doc,c=this.ngZone,a=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case Ue.Emulated:s=new At(a,u,r,this.appId,l,o,c,d);break;case Ue.ShadowDom:return new An(a,u,e,r,o,c,this.nonce,d);default:s=new Ge(a,u,r,l,o,c,d);break}i.set(r.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||t)(m(Oi),m(Pi),m(sn),m(So),m(U),m(ce),m(B),m(on))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),qe=class{constructor(n,e,r,i){this.eventManager=n,this.doc=e,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(n,e){return e?this.doc.createElementNS(En[e]||e,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,e){(Mi(n)?n.content:n).appendChild(e)}insertBefore(n,e,r){n&&(Mi(n)?n.content:n).insertBefore(e,r)}removeChild(n,e){e.remove()}selectRootElement(n,e){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new C(-5104,!1);return e||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,e,r,i){if(i){e=i+":"+e;let s=En[i];s?n.setAttributeNS(s,e,r):n.setAttribute(e,r)}else n.setAttribute(e,r)}removeAttribute(n,e,r){if(r){let i=En[r];i?n.removeAttributeNS(i,e):n.removeAttribute(`${r}:${e}`)}else n.removeAttribute(e)}addClass(n,e){n.classList.add(e)}removeClass(n,e){n.classList.remove(e)}setStyle(n,e,r,i){i&(Le.DashCase|Le.Important)?n.style.setProperty(e,r,i&Le.Important?"important":""):n.style[e]=r}removeStyle(n,e,r){r&Le.DashCase?n.style.removeProperty(e):n.style[e]=""}setProperty(n,e,r){n!=null&&(n[e]=r)}setValue(n,e){n.nodeValue=e}listen(n,e,r){if(typeof n=="string"&&(n=hn().getGlobalEventTarget(this.doc,n),!n))throw new Error(`Unsupported event target ${n} for event ${e}`);return this.eventManager.addEventListener(n,e,this.decoratePreventDefault(r))}decoratePreventDefault(n){return e=>{if(e==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(e)):n(e))===!1&&e.preventDefault()}}};function Mi(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var An=class extends qe{constructor(n,e,r,i,s,o,c,a){super(n,s,o,a),this.sharedStylesHost=e,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=xi(i.id,i.styles);for(let l of u){let d=document.createElement("style");c&&d.setAttribute("nonce",c),d.textContent=l,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,e){return super.appendChild(this.nodeOrShadowRoot(n),e)}insertBefore(n,e,r){return super.insertBefore(this.nodeOrShadowRoot(n),e,r)}removeChild(n,e){return super.removeChild(null,e)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Ge=class extends qe{constructor(n,e,r,i,s,o,c,a){super(n,s,o,c),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i,this.styles=a?xi(a,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},At=class extends Ge{constructor(n,e,r,i,s,o,c,a){let u=i+"-"+r.id;super(n,e,r,s,o,c,a,u),this.contentAttr=bo(u),this.hostAttr=Eo(u)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,e){let r=super.createElement(n,e);return super.setAttribute(r,this.contentAttr,""),r}},To=(()=>{class t extends It{constructor(e){super(e)}supports(e){return!0}addEventListener(e,r,i){return e.addEventListener(r,i,!1),()=>this.removeEventListener(e,r,i)}removeEventListener(e,r,i){return e.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||t)(m(U))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),Di=["alt","control","meta","shift"],Co={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Io={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},Ao=(()=>{class t extends It{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,r,i){let s=t.parseEventName(r),o=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>hn().onAndCancel(e,s.domEventName,o))}static parseEventName(e){let r=e.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let s=t._normalizeKey(r.pop()),o="",c=r.indexOf("code");if(c>-1&&(r.splice(c,1),o="code."),Di.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),o+=u+".")}),o+=s,r.length!=0||s.length===0)return null;let a={};return a.domEventName=i,a.fullKey=o,a}static matchEventFullKeyCode(e,r){let i=Co[e.key]||e.key,s="";return r.indexOf("code.")>-1&&(i=e.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Di.forEach(o=>{if(o!==i){let c=Io[o];c(e)&&(s+=o+".")}}),s+=i,s===r)}static eventCallback(e,r,i){return s=>{t.matchEventFullKeyCode(s,e)&&i.runGuarded(()=>r(s))}}static _normalizeKey(e){return e==="esc"?"escape":e}static{this.\u0275fac=function(r){return new(r||t)(m(U))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})();function sl(t,n){return li(f({rootComponent:t},Mo(n)))}function Mo(t){return{appProviders:[...xo,...t?.providers??[]],platformProviders:No}}function Do(){Cn.makeCurrent()}function Oo(){return new rn}function Po(){return Kr(document),document}var No=[{provide:ce,useValue:fi},{provide:Xr,useValue:Do,multi:!0},{provide:U,useFactory:Po,deps:[]}];var xo=[{provide:Gr,useValue:"root"},{provide:rn,useFactory:Oo,deps:[]},{provide:In,useClass:To,multi:!0,deps:[U,B,ce]},{provide:In,useClass:Ao,multi:!0,deps:[U]},Ai,Pi,Oi,{provide:ri,useExisting:Ai},{provide:St,useClass:vo,deps:[]},[]];var Ui=(()=>{class t{constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static{this.\u0275fac=function(r){return new(r||t)(m(U))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var Uo=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:function(r){let i=null;return r?i=new(r||t):i=m(jo),i},providedIn:"root"})}}return t})(),jo=(()=>{class t extends Uo{constructor(e){super(),this._doc=e}sanitize(e,r){if(r==null)return null;switch(e){case ue.NONE:return r;case ue.HTML:return Re(r,"HTML")?ye(r):ni(this._doc,String(r)).toString();case ue.STYLE:return Re(r,"Style")?ye(r):r;case ue.SCRIPT:if(Re(r,"Script"))return ye(r);throw new C(5200,!1);case ue.URL:return Re(r,"URL")?ye(r):ti(String(r));case ue.RESOURCE_URL:if(Re(r,"ResourceURL"))return ye(r);throw new C(5201,!1);default:throw new C(5202,!1)}}bypassSecurityTrustHtml(e){return Zr(e)}bypassSecurityTrustStyle(e){return Jr(e)}bypassSecurityTrustScript(e){return Yr(e)}bypassSecurityTrustUrl(e){return Qr(e)}bypassSecurityTrustResourceUrl(e){return ei(e)}static{this.\u0275fac=function(r){return new(r||t)(m(U))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var v="primary",ct=Symbol("RouteTitle"),xn=class{constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function Ce(t){return new xn(t)}function _o(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let s=0;s<r.length;s++){let o=r[s],c=t[s];if(o[0]===":")i[o.substring(1)]=c;else if(o!==c.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function Lo(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!V(t[e],n[e]))return!1;return!0}function V(t,n){let e=t?Un(t):void 0,r=n?Un(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let s=0;s<e.length;s++)if(i=e[s],!Bi(t[i],n[i]))return!1;return!0}function Un(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function Bi(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,s)=>r[s]===i)}else return t===n}function Vi(t){return t.length>0?t[t.length-1]:null}function ne(t){return Pr(t)?t:ui(t)?x(Promise.resolve(t)):g(t)}var Fo={exact:Gi,subset:Wi},qi={exact:$o,subset:zo,ignored:()=>!0};function ji(t,n,e){return Fo[e.paths](t.root,n.root,e.matrixParams)&&qi[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function $o(t,n){return V(t,n)}function Gi(t,n,e){if(!de(t.segments,n.segments)||!Ot(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!Gi(t.children[r],n.children[r],e))return!1;return!0}function zo(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>Bi(t[e],n[e]))}function Wi(t,n,e){return Hi(t,n,n.segments,e)}function Hi(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!de(i,e)||n.hasChildren()||!Ot(i,e,r))}else if(t.segments.length===e.length){if(!de(t.segments,e)||!Ot(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!Wi(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),s=e.slice(t.segments.length);return!de(t.segments,i)||!Ot(t.segments,i,r)||!t.children[v]?!1:Hi(t.children[v],n,s,r)}}function Ot(t,n,e){return n.every((r,i)=>qi[e](t[i].parameters,r.parameters))}var H=class{constructor(n=new T([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Ce(this.queryParams),this._queryParamMap}toString(){return qo.serialize(this)}},T=class{constructor(n,e){this.segments=n,this.children=e,this.parent=null,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Pt(this)}},le=class{constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=Ce(this.parameters),this._parameterMap}toString(){return Xi(this)}};function Bo(t,n){return de(t,n)&&t.every((e,r)=>V(e.parameters,n[r].parameters))}function de(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function Vo(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===v&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==v&&(e=e.concat(n(i,r)))}),e}var or=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:()=>new Ye,providedIn:"root"})}}return t})(),Ye=class{parse(n){let e=new kn(n);return new H(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${We(n.root,!0)}`,r=Ho(n.queryParams),i=typeof n.fragment=="string"?`#${Go(n.fragment)}`:"";return`${e}${r}${i}`}},qo=new Ye;function Pt(t){return t.segments.map(n=>Xi(n)).join("/")}function We(t,n){if(!t.hasChildren())return Pt(t);if(n){let e=t.children[v]?We(t.children[v],!1):"",r=[];return Object.entries(t.children).forEach(([i,s])=>{i!==v&&r.push(`${i}:${We(s,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=Vo(t,(r,i)=>i===v?[We(t.children[v],!1)]:[`${i}:${We(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[v]!=null?`${Pt(t)}/${e[0]}`:`${Pt(t)}/(${e.join("//")})`}}function Ki(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Mt(t){return Ki(t).replace(/%3B/gi,";")}function Go(t){return encodeURI(t)}function jn(t){return Ki(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Nt(t){return decodeURIComponent(t)}function ki(t){return Nt(t.replace(/\+/g,"%20"))}function Xi(t){return`${jn(t.path)}${Wo(t.parameters)}`}function Wo(t){return Object.entries(t).map(([n,e])=>`;${jn(n)}=${jn(e)}`).join("")}function Ho(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${Mt(e)}=${Mt(i)}`).join("&"):`${Mt(e)}=${Mt(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var Ko=/^[^\/()?;#]+/;function Dn(t){let n=t.match(Ko);return n?n[0]:""}var Xo=/^[^\/()?;=#]+/;function Zo(t){let n=t.match(Xo);return n?n[0]:""}var Jo=/^[^=?&#]+/;function Yo(t){let n=t.match(Jo);return n?n[0]:""}var Qo=/^[^&#]+/;function ea(t){let n=t.match(Qo);return n?n[0]:""}var kn=class{constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new T([],{}):new T([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[v]=new T(n,e)),r}parseSegment(){let n=Dn(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(n),new le(Nt(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=Zo(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=Dn(this.remaining);i&&(r=i,this.capture(r))}n[Nt(e)]=Nt(r)}parseQueryParam(n){let e=Yo(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let o=ea(this.remaining);o&&(r=o,this.capture(r))}let i=ki(e),s=ki(r);if(n.hasOwnProperty(i)){let o=n[i];Array.isArray(o)||(o=[o],n[i]=o),o.push(s)}else n[i]=s}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Dn(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new C(4010,!1);let s;r.indexOf(":")>-1?(s=r.slice(0,r.indexOf(":")),this.capture(s),this.capture(":")):n&&(s=v);let o=this.parseChildren();e[s]=Object.keys(o).length===1?o[v]:new T([],o),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new C(4011,!1)}};function Zi(t){return t.segments.length>0?new T([],{[v]:t}):t}function Ji(t){let n={};for(let[r,i]of Object.entries(t.children)){let s=Ji(i);if(r===v&&s.segments.length===0&&s.hasChildren())for(let[o,c]of Object.entries(s.children))n[o]=c;else(s.segments.length>0||s.hasChildren())&&(n[r]=s)}let e=new T(t.segments,n);return ta(e)}function ta(t){if(t.numberOfChildren===1&&t.children[v]){let n=t.children[v];return new T(t.segments.concat(n.segments),n.children)}return t}function Qe(t){return t instanceof H}function na(t,n,e=null,r=null){let i=Yi(t);return Qi(i,n,e,r)}function Yi(t){let n;function e(s){let o={};for(let a of s.children){let u=e(a);o[a.outlet]=u}let c=new T(s.url,o);return s===t&&(n=c),c}let r=e(t.root),i=Zi(r);return n??i}function Qi(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return On(i,i,i,e,r);let s=ra(n);if(s.toRoot())return On(i,i,new T([],{}),e,r);let o=ia(s,i,t),c=o.processChildren?Xe(o.segmentGroup,o.index,s.commands):ts(o.segmentGroup,o.index,s.commands);return On(i,o.segmentGroup,c,e,r)}function xt(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function et(t){return typeof t=="object"&&t!=null&&t.outlets}function On(t,n,e,r,i){let s={};r&&Object.entries(r).forEach(([a,u])=>{s[a]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let o;t===n?o=e:o=es(t,n,e);let c=Zi(Ji(o));return new H(c,s,i)}function es(t,n,e){let r={};return Object.entries(t.children).forEach(([i,s])=>{s===n?r[i]=e:r[i]=es(s,n,e)}),new T(t.segments,r)}var Ut=class{constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&xt(r[0]))throw new C(4003,!1);let i=r.find(et);if(i&&i!==Vi(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function ra(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new Ut(!0,0,t);let n=0,e=!1,r=t.reduce((i,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let c={};return Object.entries(s.outlets).forEach(([a,u])=>{c[a]=typeof u=="string"?u.split("/"):u}),[...i,{outlets:c}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:o===0?(s.split("/").forEach((c,a)=>{a==0&&c==="."||(a==0&&c===""?e=!0:c===".."?n++:c!=""&&i.push(c))}),i):[...i,s]},[]);return new Ut(e,n,r)}var be=class{constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function ia(t,n,e){if(t.isAbsolute)return new be(n,!0,0);if(!e)return new be(n,!1,NaN);if(e.parent===null)return new be(e,!0,0);let r=xt(t.commands[0])?0:1,i=e.segments.length-1+r;return sa(e,i,t.numberOfDoubleDots)}function sa(t,n,e){let r=t,i=n,s=e;for(;s>i;){if(s-=i,r=r.parent,!r)throw new C(4005,!1);i=r.segments.length}return new be(r,!1,i-s)}function oa(t){return et(t[0])?t[0].outlets:{[v]:t}}function ts(t,n,e){if(t??=new T([],{}),t.segments.length===0&&t.hasChildren())return Xe(t,n,e);let r=aa(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let s=new T(t.segments.slice(0,r.pathIndex),{});return s.children[v]=new T(t.segments.slice(r.pathIndex),t.children),Xe(s,0,i)}else return r.match&&i.length===0?new T(t.segments,{}):r.match&&!t.hasChildren()?_n(t,n,e):r.match?Xe(t,0,i):_n(t,n,e)}function Xe(t,n,e){if(e.length===0)return new T(t.segments,{});{let r=oa(e),i={};if(Object.keys(r).some(s=>s!==v)&&t.children[v]&&t.numberOfChildren===1&&t.children[v].segments.length===0){let s=Xe(t.children[v],n,e);return new T(t.segments,s.children)}return Object.entries(r).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(i[s]=ts(t.children[s],n,o))}),Object.entries(t.children).forEach(([s,o])=>{r[s]===void 0&&(i[s]=o)}),new T(t.segments,i)}}function aa(t,n,e){let r=0,i=n,s={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return s;let o=t.segments[i],c=e[r];if(et(c))break;let a=`${c}`,u=r<e.length-1?e[r+1]:null;if(i>0&&a===void 0)break;if(a&&u&&typeof u=="object"&&u.outlets===void 0){if(!Li(a,u,o))return s;r+=2}else{if(!Li(a,{},o))return s;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function _n(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let s=e[i];if(et(s)){let a=ca(s.outlets);return new T(r,a)}if(i===0&&xt(e[0])){let a=t.segments[n];r.push(new le(a.path,_i(e[0]))),i++;continue}let o=et(s)?s.outlets[v]:`${s}`,c=i<e.length-1?e[i+1]:null;o&&c&&xt(c)?(r.push(new le(o,_i(c))),i+=2):(r.push(new le(o,{})),i++)}return new T(r,{})}function ca(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=_n(new T([],{}),0,r))}),n}function _i(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function Li(t,n,e){return t==e.path&&V(n,e.parameters)}var Ze="imperative",O=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(O||{}),F=class{constructor(n,e){this.id=n,this.url=e}},tt=class extends F{constructor(n,e,r="imperative",i=null){super(n,e),this.type=O.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},he=class extends F{constructor(n,e,r){super(n,e),this.urlAfterRedirects=r,this.type=O.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},k=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(k||{}),Ln=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Ln||{}),W=class extends F{constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i,this.type=O.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},fe=class extends F{constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i,this.type=O.NavigationSkipped}},nt=class extends F{constructor(n,e,r,i){super(n,e),this.error=r,this.target=i,this.type=O.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},jt=class extends F{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=O.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Fn=class extends F{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=O.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},$n=class extends F{constructor(n,e,r,i,s){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=s,this.type=O.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},zn=class extends F{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=O.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Bn=class extends F{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=O.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Vn=class{constructor(n){this.route=n,this.type=O.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},qn=class{constructor(n){this.route=n,this.type=O.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Gn=class{constructor(n){this.snapshot=n,this.type=O.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Wn=class{constructor(n){this.snapshot=n,this.type=O.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Hn=class{constructor(n){this.snapshot=n,this.type=O.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Kn=class{constructor(n){this.snapshot=n,this.type=O.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var rt=class{},Ie=class{constructor(n,e){this.url=n,this.navigationBehaviorOptions=e}};function ua(t,n){return t.providers&&!t._injector&&(t._injector=oi(t.providers,n,`Route: ${t.path}`)),t._injector??n}function z(t){return t.outlet||v}function la(t,n){let e=t.filter(r=>z(r)===n);return e.push(...t.filter(r=>z(r)!==n)),e}function ut(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var Xn=class{get injector(){return ut(this.route?.snapshot)??this.rootInjector}set injector(n){}constructor(n){this.rootInjector=n,this.outlet=null,this.route=null,this.children=new Bt(this.rootInjector),this.attachRef=null}},Bt=(()=>{class t{constructor(e){this.rootInjector=e,this.contexts=new Map}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new Xn(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static{this.\u0275fac=function(r){return new(r||t)(m(ve))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),kt=class{constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=Zn(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=Zn(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=Jn(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return Jn(n,this._root).map(e=>e.value)}};function Zn(t,n){if(t===n.value)return n;for(let e of n.children){let r=Zn(t,e);if(r)return r}return null}function Jn(t,n){if(t===n.value)return[n];for(let e of n.children){let r=Jn(t,e);if(r.length)return r.unshift(n),r}return[]}var j=class{constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function Se(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var _t=class extends kt{constructor(n,e){super(n),this.snapshot=e,ar(this,n)}toString(){return this.snapshot.toString()}};function ns(t){let n=da(t),e=new P([new le("",{})]),r=new P({}),i=new P({}),s=new P({}),o=new P(""),c=new Ae(e,r,s,o,i,v,t,n.root);return c.snapshot=n.root,new _t(new j(c,[]),n)}function da(t){let n={},e={},r={},i="",s=new Ee([],n,r,i,e,v,t,null,{});return new Ft("",new j(s,[]))}var Ae=class{constructor(n,e,r,i,s,o,c,a){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=s,this.outlet=o,this.component=c,this._futureSnapshot=a,this.title=this.dataSubject?.pipe(E(u=>u[ct]))??g(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(E(n=>Ce(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(E(n=>Ce(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Lt(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:f(f({},n.params),t.params),data:f(f({},n.data),t.data),resolve:f(f(f(f({},t.data),n.data),i?.data),t._resolvedData)}:r={params:f({},t.params),data:f({},t.data),resolve:f(f({},t.data),t._resolvedData??{})},i&&is(i)&&(r.resolve[ct]=i.title),r}var Ee=class{get title(){return this.data?.[ct]}constructor(n,e,r,i,s,o,c,a,u){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s,this.outlet=o,this.component=c,this.routeConfig=a,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Ce(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Ce(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},Ft=class extends kt{constructor(n,e){super(e),this.url=n,ar(this,e)}toString(){return rs(this._root)}};function ar(t,n){n.value._routerState=t,n.children.forEach(e=>ar(t,e))}function rs(t){let n=t.children.length>0?` { ${t.children.map(rs).join(", ")} } `:"";return`${t.value}${n}`}function Pn(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,V(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),V(n.params,e.params)||t.paramsSubject.next(e.params),Lo(n.url,e.url)||t.urlSubject.next(e.url),V(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function Yn(t,n){let e=V(t.params,n.params)&&Bo(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||Yn(t.parent,n.parent))}function is(t){return typeof t.title=="string"||t.title===null}var ha=(()=>{class t{constructor(){this.activated=null,this._activatedRoute=null,this.name=v,this.activateEvents=new _e,this.deactivateEvents=new _e,this.attachEvents=new _e,this.detachEvents=new _e,this.parentContexts=h(Bt),this.location=h(ii),this.changeDetector=h(dn),this.inputBinder=h(cr,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=e;let i=this.location,o=e.snapshot.component,c=this.parentContexts.getOrCreateContext(this.name).children,a=new Qn(e,c,i.injector);this.activated=i.createComponent(o,{index:i.length,injector:a,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275dir=qr({type:t,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Hr]})}}return t})(),Qn=class t{__ngOutletInjector(n){return new t(this.route,this.childContexts,n)}constructor(n,e,r){this.route=n,this.childContexts=e,this.parent=r}get(n,e){return n===Ae?this.route:n===Bt?this.childContexts:this.parent.get(n,e)}},cr=new p("");function fa(t,n,e){let r=it(t,n._root,e?e._root:void 0);return new _t(r,n)}function it(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=pa(t,n,e);return new j(r,i)}else{if(t.shouldAttach(n.value)){let s=t.retrieve(n.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=n.value,o.children=n.children.map(c=>it(t,c)),o}}let r=ga(n.value),i=n.children.map(s=>it(t,s));return new j(r,i)}}function pa(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return it(t,r,i);return it(t,r)})}function ga(t){return new Ae(new P(t.url),new P(t.params),new P(t.queryParams),new P(t.fragment),new P(t.data),t.outlet,t.component,t)}var st=class{constructor(n,e){this.redirectTo=n,this.navigationBehaviorOptions=e}},ss="ngNavigationCancelingError";function $t(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=Qe(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=os(!1,k.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function os(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[ss]=!0,e.cancellationCode=n,e}function ma(t){return as(t)&&Qe(t.url)}function as(t){return!!t&&t[ss]}var va=(t,n,e,r)=>E(i=>(new er(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),er=class{constructor(n,e,r,i,s){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=s}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),Pn(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=Se(e);n.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,i[o],r),delete i[o]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,r)})}deactivateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(i===s)if(i.component){let o=r.getContext(i.outlet);o&&this.deactivateChildRoutes(n,e,o.children)}else this.deactivateChildRoutes(n,e,r);else s&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=Se(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);if(r&&r.outlet){let o=r.outlet.detach(),c=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:o,route:n,contexts:c})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=Se(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=Se(e);n.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],r),this.forwardEvent(new Kn(s.value.snapshot))}),n.children.length&&this.forwardEvent(new Wn(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(Pn(i),i===s)if(i.component){let o=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,o.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let o=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let c=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),o.children.onOutletReAttached(c.contexts),o.attachRef=c.componentRef,o.route=c.route.value,o.outlet&&o.outlet.attach(c.componentRef,c.route.value),Pn(c.route.value),this.activateChildRoutes(n,null,o.children)}else o.attachRef=null,o.route=i,o.outlet&&o.outlet.activateWith(i,o.injector),this.activateChildRoutes(n,null,o.children)}else this.activateChildRoutes(n,null,r)}},zt=class{constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Te=class{constructor(n,e){this.component=n,this.route=e}};function ya(t,n,e){let r=t._root,i=n?n._root:null;return He(r,i,e,[r.value])}function Ra(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function De(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!zr(t)?t:n.get(t):r}function He(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=Se(n);return t.children.forEach(o=>{wa(o,s[o.value.outlet],e,r.concat([o.value]),i),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,c])=>Je(c,e.getContext(o),i)),i}function wa(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=t.value,o=n?n.value:null,c=e?e.getContext(t.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let a=Sa(o,s,s.routeConfig.runGuardsAndResolvers);a?i.canActivateChecks.push(new zt(r)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?He(t,n,c?c.children:null,r,i):He(t,n,e,r,i),a&&c&&c.outlet&&c.outlet.isActivated&&i.canDeactivateChecks.push(new Te(c.outlet.component,o))}else o&&Je(n,c,i),i.canActivateChecks.push(new zt(r)),s.component?He(t,null,c?c.children:null,r,i):He(t,null,e,r,i);return i}function Sa(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!de(t.url,n.url);case"pathParamsOrQueryParamsChange":return!de(t.url,n.url)||!V(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Yn(t,n)||!V(t.queryParams,n.queryParams);case"paramsChange":default:return!Yn(t,n)}}function Je(t,n,e){let r=Se(t),i=t.value;Object.entries(r).forEach(([s,o])=>{i.component?n?Je(o,n.children.getContext(s),e):Je(o,null,e):Je(o,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new Te(n.outlet.component,i)):e.canDeactivateChecks.push(new Te(null,i)):e.canDeactivateChecks.push(new Te(null,i))}function lt(t){return typeof t=="function"}function ba(t){return typeof t=="boolean"}function Ea(t){return t&&lt(t.canLoad)}function Ta(t){return t&&lt(t.canActivate)}function Ca(t){return t&&lt(t.canActivateChild)}function Ia(t){return t&&lt(t.canDeactivate)}function Aa(t){return t&&lt(t.canMatch)}function cs(t){return t instanceof Nr||t?.name==="EmptyError"}var Dt=Symbol("INITIAL_VALUE");function Me(){return L(t=>Zt(t.map(n=>n.pipe(me(1),Lr(Dt)))).pipe(E(n=>{for(let e of n)if(e!==!0){if(e===Dt)return Dt;if(e===!1||Ma(e))return e}return!0}),J(n=>n!==Dt),me(1)))}function Ma(t){return Qe(t)||t instanceof st}function Da(t,n){return _(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:o}}=e;return o.length===0&&s.length===0?g(M(f({},e),{guardsResult:!0})):Oa(o,r,i,t).pipe(_(c=>c&&ba(c)?Pa(r,s,t,n):g(c)),E(c=>M(f({},e),{guardsResult:c})))})}function Oa(t,n,e,r){return x(t).pipe(_(i=>ka(i.component,i.route,e,n,r)),Y(i=>i!==!0,!0))}function Pa(t,n,e,r){return x(n).pipe(se(i=>xr(xa(i.route.parent,r),Na(i.route,r),ja(t,i.path,e),Ua(t,i.route,e))),Y(i=>i!==!0,!0))}function Na(t,n){return t!==null&&n&&n(new Hn(t)),g(!0)}function xa(t,n){return t!==null&&n&&n(new Gn(t)),g(!0)}function Ua(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return g(!0);let i=r.map(s=>Jt(()=>{let o=ut(n)??e,c=De(s,o),a=Ta(c)?c.canActivate(n,t):$(o,()=>c(n,t));return ne(a).pipe(Y())}));return g(i).pipe(Me())}function ja(t,n,e){let r=n[n.length-1],s=n.slice(0,n.length-1).reverse().map(o=>Ra(o)).filter(o=>o!==null).map(o=>Jt(()=>{let c=o.guards.map(a=>{let u=ut(o.node)??e,l=De(a,u),d=Ca(l)?l.canActivateChild(r,t):$(u,()=>l(r,t));return ne(d).pipe(Y())});return g(c).pipe(Me())}));return g(s).pipe(Me())}function ka(t,n,e,r,i){let s=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!s||s.length===0)return g(!0);let o=s.map(c=>{let a=ut(n)??i,u=De(c,a),l=Ia(u)?u.canDeactivate(t,n,e,r):$(a,()=>u(t,n,e,r));return ne(l).pipe(Y())});return g(o).pipe(Me())}function _a(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return g(!0);let s=i.map(o=>{let c=De(o,t),a=Ea(c)?c.canLoad(n,e):$(t,()=>c(n,e));return ne(a)});return g(s).pipe(Me(),us(r))}function us(t){return Mr(D(n=>{if(typeof n!="boolean")throw $t(t,n)}),E(n=>n===!0))}function La(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return g(!0);let s=i.map(o=>{let c=De(o,t),a=Aa(c)?c.canMatch(n,e):$(t,()=>c(n,e));return ne(a)});return g(s).pipe(Me(),us(r))}var ot=class{constructor(n){this.segmentGroup=n||null}},at=class extends Error{constructor(n){super(),this.urlTree=n}};function we(t){return xe(new ot(t))}function Fa(t){return xe(new C(4e3,!1))}function $a(t){return xe(os(!1,k.GuardRejected))}var tr=class{constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return g(r);if(i.numberOfChildren>1||!i.children[v])return Fa(`${n.redirectTo}`);i=i.children[v]}}applyRedirectCommands(n,e,r,i,s){if(typeof e!="string"){let c=e,{queryParams:a,fragment:u,routeConfig:l,url:d,outlet:w,params:S,data:I,title:b}=i,y=$(s,()=>c({params:S,data:I,queryParams:a,fragment:u,routeConfig:l,url:d,outlet:w,title:b}));if(y instanceof H)throw new at(y);e=y}let o=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),n,r);if(e[0]==="/")throw new at(o);return o}applyRedirectCreateUrlTree(n,e,r,i){let s=this.createSegmentGroup(n,e.root,r,i);return new H(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,s])=>{if(typeof s=="string"&&s[0]===":"){let c=s.substring(1);r[i]=e[c]}else r[i]=s}),r}createSegmentGroup(n,e,r,i){let s=this.createSegments(n,e.segments,r,i),o={};return Object.entries(e.children).forEach(([c,a])=>{o[c]=this.createSegmentGroup(n,a,r,i)}),new T(s,o)}createSegments(n,e,r,i){return e.map(s=>s.path[0]===":"?this.findPosParam(n,s,i):this.findOrReturn(s,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new C(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}},nr={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function za(t,n,e,r,i){let s=ls(t,n,e);return s.matched?(r=ua(n,r),La(r,n,e,i).pipe(E(o=>o===!0?s:f({},nr)))):g(s)}function ls(t,n,e){if(n.path==="**")return Ba(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?f({},nr):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||_o)(e,t,n);if(!i)return f({},nr);let s={};Object.entries(i.posParams??{}).forEach(([c,a])=>{s[c]=a.path});let o=i.consumed.length>0?f(f({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:o,positionalParamSegments:i.posParams??{}}}function Ba(t){return{matched:!0,parameters:t.length>0?Vi(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Fi(t,n,e,r){return e.length>0&&Ga(t,e,r)?{segmentGroup:new T(n,qa(r,new T(e,t.children))),slicedSegments:[]}:e.length===0&&Wa(t,e,r)?{segmentGroup:new T(t.segments,Va(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new T(t.segments,t.children),slicedSegments:e}}function Va(t,n,e,r){let i={};for(let s of e)if(Vt(t,n,s)&&!r[z(s)]){let o=new T([],{});i[z(s)]=o}return f(f({},r),i)}function qa(t,n){let e={};e[v]=n;for(let r of t)if(r.path===""&&z(r)!==v){let i=new T([],{});e[z(r)]=i}return e}function Ga(t,n,e){return e.some(r=>Vt(t,n,r)&&z(r)!==v)}function Wa(t,n,e){return e.some(r=>Vt(t,n,r))}function Vt(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function Ha(t,n,e){return n.length===0&&!t.children[e]}var rr=class{};function Ka(t,n,e,r,i,s,o="emptyOnly"){return new ir(t,n,e,r,i,o,s).recognize()}var Xa=31,ir=class{constructor(n,e,r,i,s,o,c){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=c,this.applyRedirects=new tr(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(n){return new C(4002,`'${n.segmentGroup}'`)}recognize(){let n=Fi(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(E(({children:e,rootSnapshot:r})=>{let i=new j(r,e),s=new Ft("",i),o=na(r,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),{state:s,tree:o}}))}match(n){let e=new Ee([],Object.freeze({}),Object.freeze(f({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),v,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,v,e).pipe(E(r=>({children:r,rootSnapshot:e})),ge(r=>{if(r instanceof at)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof ot?this.noMatchError(r):r}))}processSegmentGroup(n,e,r,i,s){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r,s):this.processSegment(n,e,r,r.segments,i,!0,s).pipe(E(o=>o instanceof j?[o]:[]))}processChildren(n,e,r,i){let s=[];for(let o of Object.keys(r.children))o==="primary"?s.unshift(o):s.push(o);return x(s).pipe(se(o=>{let c=r.children[o],a=la(e,o);return this.processSegmentGroup(n,a,c,o,i)}),ft((o,c)=>(o.push(...c),o)),Yt(null),kr(),_(o=>{if(o===null)return we(r);let c=ds(o);return Za(c),g(c)}))}processSegment(n,e,r,i,s,o,c){return x(e).pipe(se(a=>this.processSegmentAgainstRoute(a._injector??n,e,a,r,i,s,o,c).pipe(ge(u=>{if(u instanceof ot)return g(null);throw u}))),Y(a=>!!a),ge(a=>{if(cs(a))return Ha(r,i,s)?g(new rr):we(r);throw a}))}processSegmentAgainstRoute(n,e,r,i,s,o,c,a){return z(r)!==o&&(o===v||!Vt(i,s,r))?we(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,s,o,a):this.allowRedirects&&c?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,s,o,a):we(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,s,o,c){let{matched:a,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:w}=ls(e,i,s);if(!a)return we(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Xa&&(this.allowRedirects=!1));let S=new Ee(s,u,Object.freeze(f({},this.urlTree.queryParams)),this.urlTree.fragment,$i(i),z(i),i.component??i._loadedComponent??null,i,zi(i)),I=Lt(S,c,this.paramsInheritanceStrategy);S.params=Object.freeze(I.params),S.data=Object.freeze(I.data);let b=this.applyRedirects.applyRedirectCommands(l,i.redirectTo,d,S,n);return this.applyRedirects.lineralizeSegments(i,b).pipe(_(y=>this.processSegment(n,r,e,y.concat(w),o,!1,c)))}matchSegmentAgainstRoute(n,e,r,i,s,o){let c=za(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),c.pipe(L(a=>a.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(L(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:w,remainingSegments:S}=a,I=new Ee(w,d,Object.freeze(f({},this.urlTree.queryParams)),this.urlTree.fragment,$i(r),z(r),r.component??r._loadedComponent??null,r,zi(r)),b=Lt(I,o,this.paramsInheritanceStrategy);I.params=Object.freeze(b.params),I.data=Object.freeze(b.data);let{segmentGroup:y,slicedSegments:N}=Fi(e,w,S,u);if(N.length===0&&y.hasChildren())return this.processChildren(l,u,y,I).pipe(E(A=>new j(I,A)));if(u.length===0&&N.length===0)return g(new j(I,[]));let X=z(r)===s;return this.processSegment(l,u,y,N,X?v:s,!0,I).pipe(E(A=>new j(I,A instanceof j?[A]:[])))}))):we(e)))}getChildConfig(n,e,r){return e.children?g({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?g({routes:e._loadedRoutes,injector:e._loadedInjector}):_a(n,e,r,this.urlSerializer).pipe(_(i=>i?this.configLoader.loadChildren(n,e).pipe(D(s=>{e._loadedRoutes=s.routes,e._loadedInjector=s.injector})):$a(e))):g({routes:[],injector:n})}};function Za(t){t.sort((n,e)=>n.value.outlet===v?-1:e.value.outlet===v?1:n.value.outlet.localeCompare(e.value.outlet))}function Ja(t){let n=t.value.routeConfig;return n&&n.path===""}function ds(t){let n=[],e=new Set;for(let r of t){if(!Ja(r)){n.push(r);continue}let i=n.find(s=>r.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=ds(r.children);n.push(new j(r.value,i))}return n.filter(r=>!e.has(r))}function $i(t){return t.data||{}}function zi(t){return t.resolve||{}}function Ya(t,n,e,r,i,s){return _(o=>Ka(t,n,e,r,o.extractedUrl,i,s).pipe(E(({state:c,tree:a})=>M(f({},o),{targetSnapshot:c,urlAfterRedirects:a}))))}function Qa(t,n){return _(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return g(e);let s=new Set(i.map(a=>a.route)),o=new Set;for(let a of s)if(!o.has(a))for(let u of hs(a))o.add(u);let c=0;return x(o).pipe(se(a=>s.has(a)?ec(a,r,t,n):(a.data=Lt(a,a.parent,t).resolve,g(void 0))),D(()=>c++),Qt(1),_(a=>c===o.size?g(e):Z))})}function hs(t){let n=t.children.map(e=>hs(e)).flat();return[t,...n]}function ec(t,n,e,r){let i=t.routeConfig,s=t._resolve;return i?.title!==void 0&&!is(i)&&(s[ct]=i.title),tc(s,t,n,r).pipe(E(o=>(t._resolvedData=o,t.data=Lt(t,t.parent,e).resolve,null)))}function tc(t,n,e,r){let i=Un(t);if(i.length===0)return g({});let s={};return x(i).pipe(_(o=>nc(t[o],n,e,r).pipe(Y(),D(c=>{if(c instanceof st)throw $t(new Ye,c);s[o]=c}))),Qt(1),Ur(s),ge(o=>cs(o)?Z:xe(o)))}function nc(t,n,e,r){let i=ut(n)??r,s=De(t,i),o=s.resolve?s.resolve(n,e):$(i,()=>s(n,e));return ne(o)}function Nn(t){return L(n=>{let e=t(n);return e?x(e).pipe(E(()=>n)):g(n)})}var fs=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(s=>s.outlet===v);return r}getResolvedTitleForRoute(e){return e.data[ct]}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:()=>h(rc),providedIn:"root"})}}return t})(),rc=(()=>{class t extends fs{constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||t)(m(Ui))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),ur=new p("",{providedIn:"root",factory:()=>({})}),ic=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275cmp=Vr({type:t,selectors:[["ng-component"]],standalone:!0,features:[ci],decls:1,vars:0,template:function(r,i){r&1&&ai(0,"router-outlet")},dependencies:[ha],encapsulation:2})}}return t})();function lr(t){let n=t.children&&t.children.map(lr),e=n?M(f({},t),{children:n}):f({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==v&&(e.component=ic),e}var dr=new p(""),sc=(()=>{class t{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=h(un)}loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return g(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=ne(e.loadComponent()).pipe(E(ps),D(s=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=s}),oe(()=>{this.componentLoaders.delete(e)})),i=new Xt(r,()=>new q).pipe(Kt());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return g({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let s=oc(r,this.compiler,e,this.onLoadEndListener).pipe(oe(()=>{this.childrenLoaders.delete(r)})),o=new Xt(s,()=>new q).pipe(Kt());return this.childrenLoaders.set(r,o),o}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function oc(t,n,e,r){return ne(t.loadChildren()).pipe(E(ps),_(i=>i instanceof si||Array.isArray(i)?g(i):x(n.compileModuleAsync(i))),E(i=>{r&&r(t);let s,o,c=!1;return Array.isArray(i)?(o=i,c=!0):(s=i.create(e).injector,o=s.get(dr,[],{optional:!0,self:!0}).flat()),{routes:o.map(lr),injector:s}}))}function ac(t){return t&&typeof t=="object"&&"default"in t}function ps(t){return ac(t)?t.default:t}var hr=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:()=>h(cc),providedIn:"root"})}}return t})(),cc=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),uc=new p("");var lc=new p(""),dc=(()=>{class t{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new q,this.transitionAbortSubject=new q,this.configLoader=h(sc),this.environmentInjector=h(ve),this.urlSerializer=h(or),this.rootContexts=h(Bt),this.location=h(yt),this.inputBindingEnabled=h(cr,{optional:!0})!==null,this.titleStrategy=h(fs),this.options=h(ur,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=h(hr),this.createViewTransition=h(uc,{optional:!0}),this.navigationErrorHandler=h(lc,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>g(void 0),this.rootComponentType=null;let e=i=>this.events.next(new Vn(i)),r=i=>this.events.next(new qn(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(M(f(f({},this.transitions.value),e),{id:r}))}setupNavigations(e,r,i){return this.transitions=new P({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:Ze,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(J(s=>s.id!==0),E(s=>M(f({},s),{extractedUrl:this.urlHandlingStrategy.extract(s.rawUrl)})),L(s=>{let o=!1,c=!1;return g(s).pipe(L(a=>{if(this.navigationId>s.id)return this.cancelNavigationTransition(s,"",k.SupersededByNewNavigation),Z;this.currentTransition=s,this.currentNavigation={id:a.id,initialUrl:a.rawUrl,extractedUrl:a.extractedUrl,targetBrowserUrl:typeof a.extras.browserUrl=="string"?this.urlSerializer.parse(a.extras.browserUrl):a.extras.browserUrl,trigger:a.source,extras:a.extras,previousNavigation:this.lastSuccessfulNavigation?M(f({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let u=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),l=a.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!u&&l!=="reload"){let d="";return this.events.next(new fe(a.id,this.urlSerializer.serialize(a.rawUrl),d,Ln.IgnoredSameUrlNavigation)),a.resolve(!1),Z}if(this.urlHandlingStrategy.shouldProcessUrl(a.rawUrl))return g(a).pipe(L(d=>{let w=this.transitions?.getValue();return this.events.next(new tt(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),w!==this.transitions?.getValue()?Z:Promise.resolve(d)}),Ya(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),D(d=>{s.targetSnapshot=d.targetSnapshot,s.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=M(f({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let w=new jt(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(w)}));if(u&&this.urlHandlingStrategy.shouldProcessUrl(a.currentRawUrl)){let{id:d,extractedUrl:w,source:S,restoredState:I,extras:b}=a,y=new tt(d,this.urlSerializer.serialize(w),S,I);this.events.next(y);let N=ns(this.rootComponentType).snapshot;return this.currentTransition=s=M(f({},a),{targetSnapshot:N,urlAfterRedirects:w,extras:M(f({},b),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=w,g(s)}else{let d="";return this.events.next(new fe(a.id,this.urlSerializer.serialize(a.extractedUrl),d,Ln.IgnoredByUrlHandlingStrategy)),a.resolve(!1),Z}}),D(a=>{let u=new Fn(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}),E(a=>(this.currentTransition=s=M(f({},a),{guards:ya(a.targetSnapshot,a.currentSnapshot,this.rootContexts)}),s)),Da(this.environmentInjector,a=>this.events.next(a)),D(a=>{if(s.guardsResult=a.guardsResult,a.guardsResult&&typeof a.guardsResult!="boolean")throw $t(this.urlSerializer,a.guardsResult);let u=new $n(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot,!!a.guardsResult);this.events.next(u)}),J(a=>a.guardsResult?!0:(this.cancelNavigationTransition(a,"",k.GuardRejected),!1)),Nn(a=>{if(a.guards.canActivateChecks.length)return g(a).pipe(D(u=>{let l=new zn(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}),L(u=>{let l=!1;return g(u).pipe(Qa(this.paramsInheritanceStrategy,this.environmentInjector),D({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(u,"",k.NoDataFromResolver)}}))}),D(u=>{let l=new Bn(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}))}),Nn(a=>{let u=l=>{let d=[];l.routeConfig?.loadComponent&&!l.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(l.routeConfig).pipe(D(w=>{l.component=w}),E(()=>{})));for(let w of l.children)d.push(...u(w));return d};return Zt(u(a.targetSnapshot.root)).pipe(Yt(null),me(1))}),Nn(()=>this.afterPreactivation()),L(()=>{let{currentSnapshot:a,targetSnapshot:u}=s,l=this.createViewTransition?.(this.environmentInjector,a.root,u.root);return l?x(l).pipe(E(()=>s)):g(s)}),E(a=>{let u=fa(e.routeReuseStrategy,a.targetSnapshot,a.currentRouterState);return this.currentTransition=s=M(f({},a),{targetRouterState:u}),this.currentNavigation.targetRouterState=u,s}),D(()=>{this.events.next(new rt)}),va(this.rootContexts,e.routeReuseStrategy,a=>this.events.next(a),this.inputBindingEnabled),me(1),D({next:a=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new he(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects))),this.titleStrategy?.updateTitle(a.targetRouterState.snapshot),a.resolve(!0)},complete:()=>{o=!0}}),Fr(this.transitionAbortSubject.pipe(D(a=>{throw a}))),oe(()=>{!o&&!c&&this.cancelNavigationTransition(s,"",k.SupersededByNewNavigation),this.currentTransition?.id===s.id&&(this.currentNavigation=null,this.currentTransition=null)}),ge(a=>{if(c=!0,as(a))this.events.next(new W(s.id,this.urlSerializer.serialize(s.extractedUrl),a.message,a.cancellationCode)),ma(a)?this.events.next(new Ie(a.url,a.navigationBehaviorOptions)):s.resolve(!1);else{let u=new nt(s.id,this.urlSerializer.serialize(s.extractedUrl),a,s.targetSnapshot??void 0);try{let l=$(this.environmentInjector,()=>this.navigationErrorHandler?.(u));if(l instanceof st){let{message:d,cancellationCode:w}=$t(this.urlSerializer,l);this.events.next(new W(s.id,this.urlSerializer.serialize(s.extractedUrl),d,w)),this.events.next(new Ie(l.redirectTo,l.navigationBehaviorOptions))}else{this.events.next(u);let d=e.errorHandler(a);s.resolve(!!d)}}catch(l){this.options.resolveNavigationPromiseOnError?s.resolve(!1):s.reject(l)}}return Z}))}))}cancelNavigationTransition(e,r,i){let s=new W(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(s),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function hc(t){return t!==Ze}var fc=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:()=>h(pc),providedIn:"root"})}}return t})(),sr=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},pc=(()=>{class t extends sr{static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=je(t)))(i||t)}})()}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),gs=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:()=>h(gc),providedIn:"root"})}}return t})(),gc=(()=>{class t extends gs{constructor(){super(...arguments),this.location=h(yt),this.urlSerializer=h(or),this.options=h(ur,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=h(hr),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new H,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=ns(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&e(r.url,r.state)})}handleRouterEvent(e,r){if(e instanceof tt)this.stateMemento=this.createStateMemento();else if(e instanceof fe)this.rawUrlTree=r.initialUrl;else if(e instanceof jt){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??i,r)}}else e instanceof rt?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):e instanceof W&&(e.code===k.GuardRejected||e.code===k.NoDataFromResolver)?this.restoreHistory(r):e instanceof nt?this.restoreHistory(r,!0):e instanceof he&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,r){let i=e instanceof H?this.urlSerializer.serialize(e):e;if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let s=this.browserPageId,o=f(f({},r.extras.state),this.generateNgRouterState(r.id,s));this.location.replaceState(i,"",o)}else{let s=f(f({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",s)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.currentUrlTree===e.finalUrl&&s===0&&(this.resetState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(e),this.resetUrlToCurrentUrlTree())}resetState(e){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=je(t)))(i||t)}})()}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Ke=function(t){return t[t.COMPLETE=0]="COMPLETE",t[t.FAILED=1]="FAILED",t[t.REDIRECTING=2]="REDIRECTING",t}(Ke||{});function mc(t,n){t.events.pipe(J(e=>e instanceof he||e instanceof W||e instanceof nt||e instanceof fe),E(e=>e instanceof he||e instanceof fe?Ke.COMPLETE:(e instanceof W?e.code===k.Redirect||e.code===k.SupersededByNewNavigation:!1)?Ke.REDIRECTING:Ke.FAILED),J(e=>e!==Ke.REDIRECTING),me(1)).subscribe(()=>{n()})}function vc(t){throw t}var yc={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Rc={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ms=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=h(gt),this.stateManager=h(gs),this.options=h(ur,{optional:!0})||{},this.pendingTasks=h(ke),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=h(dc),this.urlSerializer=h(or),this.location=h(yt),this.urlHandlingStrategy=h(hr),this._events=new q,this.errorHandler=this.options.errorHandler||vc,this.navigated=!1,this.routeReuseStrategy=h(fc),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=h(dr,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!h(cr,{optional:!0}),this.eventsSubscription=new Ar,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(r,s),r instanceof W&&r.code!==k.Redirect&&r.code!==k.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof he)this.navigated=!0;else if(r instanceof Ie){let o=r.navigationBehaviorOptions,c=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),a=f({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||hc(i.source)},o);this.scheduleNavigation(c,Ze,null,a,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}Sc(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ze,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(e,"popstate",r)},0)})}navigateToSyncWithBrowser(e,r,i){let s={replaceUrl:!0},o=i?.navigationId?i:null;if(i){let a=f({},i);delete a.navigationId,delete a.\u0275routerPageId,Object.keys(a).length!==0&&(s.state=a)}let c=this.parseUrl(e);this.scheduleNavigation(c,r,o,s)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(lr),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:s,fragment:o,queryParamsHandling:c,preserveFragment:a}=r,u=a?this.currentUrlTree.fragment:o,l=null;switch(c??this.options.defaultQueryParamsHandling){case"merge":l=f(f({},this.currentUrlTree.queryParams),s);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=s||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let w=i?i.snapshot:this.routerState.snapshot.root;d=Yi(w)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),d=this.currentUrlTree.root}return Qi(d,e,l,u??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=Qe(e)?e:this.parseUrl(e),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,Ze,null,r)}navigate(e,r={skipLocationChange:!1}){return wc(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=f({},yc):r===!1?i=f({},Rc):i=r,Qe(e))return ji(this.currentUrlTree,e,i);let s=this.parseUrl(e);return ji(this.currentUrlTree,s,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,s])=>(s!=null&&(r[i]=s),r),{})}scheduleNavigation(e,r,i,s,o){if(this.disposed)return Promise.resolve(!1);let c,a,u;o?(c=o.resolve,a=o.reject,u=o.promise):u=new Promise((d,w)=>{c=d,a=w});let l=this.pendingTasks.add();return mc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:s,resolve:c,reject:a,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function wc(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new C(4008,!1)}function Sc(t){return!(t instanceof rt)&&!(t instanceof Ie)}var bc=new p("");function Hl(t,...n){return ae([{provide:dr,multi:!0,useValue:t},[],{provide:Ae,useFactory:Ec,deps:[ms]},{provide:cn,multi:!0,useFactory:Tc},n.map(e=>e.\u0275providers)])}function Ec(t){return t.routerState.root}function Tc(){let t=h(tn);return n=>{let e=t.get(mt);if(n!==e.components[0])return;let r=t.get(ms),i=t.get(Cc);t.get(Ic)===1&&r.initialNavigation(),t.get(Ac,null,en.Optional)?.setUpPreloading(),t.get(bc,null,en.Optional)?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var Cc=new p("",{factory:()=>new q}),Ic=new p("",{providedIn:"root",factory:()=>1});var Ac=new p("");function fr(t,n){let e=!n?.manualCleanup;e&&!n?.injector&&Wr(fr);let r=e?n?.injector?.get(nn)??h(nn):null,i=Mc(n?.equal),s;n?.requireSync?s=an({kind:0},{equal:i}):s=an({kind:1,value:n?.initialValue},{equal:i});let o=t.subscribe({next:c=>s.set({kind:1,value:c}),error:c=>{if(n?.rejectErrors)throw c;s.set({kind:2,error:c})}});if(n?.requireSync&&s().kind===0)throw new C(601,!1);return r?.onDestroy(o.unsubscribe.bind(o)),vt(()=>{let c=s();switch(c.kind){case 1:return c.value;case 2:throw c.error;case 0:throw new C(601,!1)}},{equal:n?.equal})}function Mc(t=Object.is){return(n,e)=>n.kind===1&&e.kind===1&&t(n.value,e.value)}var mr={};function K(t,n){if(mr[t]=(mr[t]||0)+1,typeof n=="function")return pr(t,(...r)=>M(f({},n(...r)),{type:t}));switch(n?n._as:"empty"){case"empty":return pr(t,()=>({type:t}));case"props":return pr(t,r=>M(f({},r),{type:t}));default:throw new Error("Unexpected config.")}}function pe(){return{_as:"props",_p:void 0}}function pr(t,n){return Object.defineProperty(n,"type",{value:t,writable:!1})}var Os="@ngrx/store/init",Oe=(()=>{class t extends P{constructor(){super({type:Os})}next(e){if(typeof e=="function")throw new TypeError(`
        Dispatch expected an object, instead it received a function.
        If you're using the createAction function, make sure to invoke the function
        before dispatching the action. For example, someAction should be someAction().`);if(typeof e>"u")throw new TypeError("Actions must be objects");if(typeof e.type>"u")throw new TypeError("Actions must have a type property");super.next(e)}complete(){}ngOnDestroy(){super.complete()}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),Dc=[Oe],Ps=new p("@ngrx/store Internal Root Guard"),vs=new p("@ngrx/store Internal Initial State"),Sr=new p("@ngrx/store Initial State"),Ns=new p("@ngrx/store Reducer Factory"),ys=new p("@ngrx/store Internal Reducer Factory Provider"),xs=new p("@ngrx/store Initial Reducers"),gr=new p("@ngrx/store Internal Initial Reducers"),ud=new p("@ngrx/store Store Features"),Rs=new p("@ngrx/store Internal Store Reducers"),ld=new p("@ngrx/store Internal Feature Reducers"),dd=new p("@ngrx/store Internal Feature Configs"),hd=new p("@ngrx/store Internal Store Features"),fd=new p("@ngrx/store Internal Feature Reducers Token"),pd=new p("@ngrx/store Feature Reducers"),ws=new p("@ngrx/store User Provided Meta Reducers"),qt=new p("@ngrx/store Meta Reducers"),Ss=new p("@ngrx/store Internal Resolved Meta Reducers"),bs=new p("@ngrx/store User Runtime Checks Config"),Es=new p("@ngrx/store Internal User Runtime Checks Config"),dt=new p("@ngrx/store Internal Runtime Checks"),Us=new p("@ngrx/store Check if Action types are unique"),Ts=new p("@ngrx/store Root Store Provider"),gd=new p("@ngrx/store Feature State Provider");function Oc(t,n={}){let e=Object.keys(t),r={};for(let s=0;s<e.length;s++){let o=e[s];typeof t[o]=="function"&&(r[o]=t[o])}let i=Object.keys(r);return function(o,c){o=o===void 0?n:o;let a=!1,u={};for(let l=0;l<i.length;l++){let d=i[l],w=r[d],S=o[d],I=w(S,c);u[d]=I,a=a||I!==S}return a?u:o}}function Pc(t,n){return Object.keys(t).filter(e=>e!==n).reduce((e,r)=>Object.assign(e,{[r]:t[r]}),{})}function js(...t){return function(n){if(t.length===0)return n;let e=t[t.length-1];return t.slice(0,-1).reduceRight((i,s)=>s(i),e(n))}}function ks(t,n){return Array.isArray(n)&&n.length>0&&(t=js.apply(null,[...n,t])),(e,r)=>{let i=t(e);return(s,o)=>(s=s===void 0?r:s,i(s,o))}}function Nc(t){let n=Array.isArray(t)&&t.length>0?js(...t):e=>e;return(e,r)=>(e=n(e),(i,s)=>(i=i===void 0?r:i,e(i,s)))}var ht=class extends ie{},Gt=class extends Oe{},xc="@ngrx/store/update-reducers",vr=(()=>{class t extends P{get currentReducers(){return this.reducers}constructor(e,r,i,s){super(s(i,r)),this.dispatcher=e,this.initialState=r,this.reducers=i,this.reducerFactory=s}addFeature(e){this.addFeatures([e])}addFeatures(e){let r=e.reduce((i,{reducers:s,reducerFactory:o,metaReducers:c,initialState:a,key:u})=>{let l=typeof s=="function"?Nc(c)(s,a):ks(o,c)(s,a);return i[u]=l,i},{});this.addReducers(r)}removeFeature(e){this.removeFeatures([e])}removeFeatures(e){this.removeReducers(e.map(r=>r.key))}addReducer(e,r){this.addReducers({[e]:r})}addReducers(e){this.reducers=f(f({},this.reducers),e),this.updateReducers(Object.keys(e))}removeReducer(e){this.removeReducers([e])}removeReducers(e){e.forEach(r=>{this.reducers=Pc(this.reducers,r)}),this.updateReducers(e)}updateReducers(e){this.next(this.reducerFactory(this.reducers,this.initialState)),this.dispatcher.next({type:xc,features:e})}ngOnDestroy(){this.complete()}static{this.\u0275fac=function(r){return new(r||t)(m(Gt),m(Sr),m(xs),m(Ns))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),Uc=[vr,{provide:ht,useExisting:vr},{provide:Gt,useExisting:Oe}],br=(()=>{class t extends q{ngOnDestroy(){this.complete()}static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=je(t)))(i||t)}})()}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),jc=[br],Wt=class extends ie{},Cs=(()=>{class t extends P{static{this.INIT=Os}constructor(e,r,i,s){super(s);let c=e.pipe(Or(Dr)).pipe($r(r)),a={state:s},u=c.pipe(ft(kc,a));this.stateSubscription=u.subscribe(({state:l,action:d})=>{this.next(l),i.next(d)}),this.state=fr(this,{manualCleanup:!0,requireSync:!0})}ngOnDestroy(){this.stateSubscription.unsubscribe(),this.complete()}static{this.\u0275fac=function(r){return new(r||t)(m(Oe),m(ht),m(br),m(Sr))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})();function kc(t={state:void 0},[n,e]){let{state:r}=t;return{state:e(r,n),action:n}}var _c=[Cs,{provide:Wt,useExisting:Cs}],Er=(()=>{class t extends ie{constructor(e,r,i){super(),this.actionsObserver=r,this.reducerManager=i,this.source=e,this.state=e.state}select(e,...r){return Fc.call(null,e,...r)(this)}selectSignal(e,r){return vt(()=>e(this.state()),r)}lift(e){let r=new t(this,this.actionsObserver,this.reducerManager);return r.operator=e,r}dispatch(e){this.actionsObserver.next(e)}next(e){this.actionsObserver.next(e)}error(e){this.actionsObserver.error(e)}complete(){this.actionsObserver.complete()}addReducer(e,r){this.reducerManager.addReducer(e,r)}removeReducer(e){this.reducerManager.removeReducer(e)}static{this.\u0275fac=function(r){return new(r||t)(m(Wt),m(Oe),m(vr))}}static{this.\u0275prov=R({token:t,factory:t.\u0275fac})}}return t})(),Lc=[Er];function Fc(t,n,...e){return function(i){let s;if(typeof t=="string"){let o=[n,...e].filter(Boolean);s=i.pipe(_r(t,...o))}else if(typeof t=="function")s=i.pipe(E(o=>t(o,n)));else throw new TypeError(`Unexpected type '${typeof t}' in select operator, expected 'string' or 'function'`);return s.pipe(jr())}}var Tr="https://ngrx.io/guide/store/configuration/runtime-checks";function Is(t){return t===void 0}function As(t){return t===null}function _s(t){return Array.isArray(t)}function $c(t){return typeof t=="string"}function zc(t){return typeof t=="boolean"}function Bc(t){return typeof t=="number"}function Ls(t){return typeof t=="object"&&t!==null}function Vc(t){return Ls(t)&&!_s(t)}function qc(t){if(!Vc(t))return!1;let n=Object.getPrototypeOf(t);return n===Object.prototype||n===null}function yr(t){return typeof t=="function"}function Gc(t){return yr(t)&&t.hasOwnProperty("\u0275cmp")}function Wc(t,n){return Object.prototype.hasOwnProperty.call(t,n)}var Hc=!1;function Kc(){return Hc}function Ms(t,n){return t===n}function Xc(t,n,e){for(let r=0;r<t.length;r++)if(!e(t[r],n[r]))return!0;return!1}function Fs(t,n=Ms,e=Ms){let r=null,i=null,s;function o(){r=null,i=null}function c(l=void 0){s={result:l}}function a(){s=void 0}function u(){if(s!==void 0)return s.result;if(!r)return i=t.apply(null,arguments),r=arguments,i;if(!Xc(arguments,r,n))return i;let l=t.apply(null,arguments);return r=arguments,e(i,l)?i:(i=l,l)}return{memoized:u,reset:o,setResult:c,clearResult:a}}function Zc(...t){return Yc(Fs)(...t)}function Jc(t,n,e,r){if(e===void 0){let s=n.map(o=>o(t));return r.memoized.apply(null,s)}let i=n.map(s=>s(t,e));return r.memoized.apply(null,[...i,e])}function Yc(t,n={stateFn:Jc}){return function(...e){let r=e;if(Array.isArray(r[0])){let[l,...d]=r;r=[...l,...d]}else r.length===1&&Qc(r[0])&&(r=eu(r[0]));let i=r.slice(0,r.length-1),s=r[r.length-1],o=i.filter(l=>l.release&&typeof l.release=="function"),c=t(function(...l){return s.apply(null,l)}),a=Fs(function(l,d){return n.stateFn.apply(null,[l,i,d,c])});function u(){a.reset(),c.reset(),o.forEach(l=>l.release())}return Object.assign(a.memoized,{release:u,projector:c.memoized,setResult:a.setResult,clearResult:a.clearResult})}}function md(t){return Zc(n=>{let e=n[t];return!Kc()&&ln()&&!(t in n)&&console.warn(`@ngrx/store: The feature name "${t}" does not exist in the state, therefore createFeatureSelector cannot access it.  Be sure it is imported in a loaded module using StoreModule.forRoot('${t}', ...) or StoreModule.forFeature('${t}', ...).  If the default state is intended to be undefined, as is the case with router state, this development-only warning message can be ignored.`),e},n=>n)}function Qc(t){return!!t&&typeof t=="object"&&Object.values(t).every(n=>typeof n=="function")}function eu(t){let n=Object.values(t),e=Object.keys(t),r=(...i)=>e.reduce((s,o,c)=>M(f({},s),{[o]:i[c]}),{});return[...n,r]}function tu(t){return t instanceof p?h(t):t}function nu(t){return typeof t=="function"?t():t}function ru(t,n){return t.concat(n)}function iu(){if(h(Er,{optional:!0,skipSelf:!0}))throw new TypeError("The root Store has been provided more than once. Feature modules should provide feature states instead.");return"guarded"}function su(t,n){return function(e,r){let i=n.action(r)?Rr(r):r,s=t(e,i);return n.state()?Rr(s):s}}function Rr(t){Object.freeze(t);let n=yr(t);return Object.getOwnPropertyNames(t).forEach(e=>{if(!e.startsWith("\u0275")&&Wc(t,e)&&(!n||e!=="caller"&&e!=="callee"&&e!=="arguments")){let r=t[e];(Ls(r)||yr(r))&&!Object.isFrozen(r)&&Rr(r)}}),t}function ou(t,n){return function(e,r){if(n.action(r)){let s=wr(r);Ds(s,"action")}let i=t(e,r);if(n.state()){let s=wr(i);Ds(s,"state")}return i}}function wr(t,n=[]){return(Is(t)||As(t))&&n.length===0?{path:["root"],value:t}:Object.keys(t).reduce((r,i)=>{if(r)return r;let s=t[i];return Gc(s)?r:Is(s)||As(s)||Bc(s)||zc(s)||$c(s)||_s(s)?!1:qc(s)?wr(s,[...n,i]):{path:[...n,i],value:s}},!1)}function Ds(t,n){if(t===!1)return;let e=t.path.join("."),r=new Error(`Detected unserializable ${n} at "${e}". ${Tr}#strict${n}serializability`);throw r.value=t.value,r.unserializablePath=e,r}function au(t,n){return function(e,r){if(n.action(r)&&!B.isInAngularZone())throw new Error(`Action '${r.type}' running outside NgZone. ${Tr}#strictactionwithinngzone`);return t(e,r)}}function cu(t){return ln()?f({strictStateSerializability:!1,strictActionSerializability:!1,strictStateImmutability:!0,strictActionImmutability:!0,strictActionWithinNgZone:!1,strictActionTypeUniqueness:!1},t):{strictStateSerializability:!1,strictActionSerializability:!1,strictStateImmutability:!1,strictActionImmutability:!1,strictActionWithinNgZone:!1,strictActionTypeUniqueness:!1}}function uu({strictActionSerializability:t,strictStateSerializability:n}){return e=>t||n?ou(e,{action:r=>t&&!Cr(r),state:()=>n}):e}function lu({strictActionImmutability:t,strictStateImmutability:n}){return e=>t||n?su(e,{action:r=>t&&!Cr(r),state:()=>n}):e}function Cr(t){return t.type.startsWith("@ngrx")}function du({strictActionWithinNgZone:t}){return n=>t?au(n,{action:e=>t&&!Cr(e)}):n}function hu(t){return[{provide:Es,useValue:t},{provide:bs,useFactory:pu,deps:[Es]},{provide:dt,deps:[bs],useFactory:cu},{provide:qt,multi:!0,deps:[dt],useFactory:lu},{provide:qt,multi:!0,deps:[dt],useFactory:uu},{provide:qt,multi:!0,deps:[dt],useFactory:du}]}function fu(){return[{provide:Us,multi:!0,deps:[dt],useFactory:gu}]}function pu(t){return t}function gu(t){if(!t.strictActionTypeUniqueness)return;let n=Object.entries(mr).filter(([,e])=>e>1).map(([e])=>e);if(n.length)throw new Error(`Action types are registered more than once, ${n.map(e=>`"${e}"`).join(", ")}. ${Tr}#strictactiontypeuniqueness`)}function mu(t={},n={}){return[{provide:Ps,useFactory:iu},{provide:vs,useValue:n.initialState},{provide:Sr,useFactory:nu,deps:[vs]},{provide:gr,useValue:t},{provide:Rs,useExisting:t instanceof p?t:gr},{provide:xs,deps:[gr,[new Br(Rs)]],useFactory:tu},{provide:ws,useValue:n.metaReducers?n.metaReducers:[]},{provide:Ss,deps:[qt,ws],useFactory:ru},{provide:ys,useValue:n.reducerFactory?n.reducerFactory:Oc},{provide:Ns,deps:[ys,Ss],useFactory:ks},Dc,Uc,jc,_c,Lc,hu(n.runtimeChecks),fu()]}function vu(){h(Oe),h(ht),h(br),h(Er),h(Ps,{optional:!0}),h(Us,{optional:!0})}var yu=[{provide:Ts,useFactory:vu},{provide:pt,multi:!0,useFactory(){return()=>h(Ts)}}];function vd(t,n){return ae([...mu(t,n),yu])}function yd(...t){let n=t.pop(),e=t.map(r=>r.type);return{reducer:n,types:e}}function Rd(t,...n){let e=new Map;for(let r of n)for(let i of r.types){let s=e.get(i);if(s){let o=(c,a)=>r.reducer(s(c,a),a);e.set(i,o)}else e.set(i,r.reducer)}return function(r=t,i){let s=e.get(i.type);return s?s(r,i):r}}var Ed=K("[User List] Load Users",pe()),Td=K("[User List] Load Users Success",pe()),Cd=K("[User List] Load Users Failure",pe()),Id=K("[User Detail] Load User Detail",pe()),Ad=K("[User Detail] Load User Detail Success",pe()),Md=K("[User Detail] Load User Detail Failure",pe()),Dd=K("[User] Clear Selected User"),Od=K("[User] Clear Error");export{Hs as a,Uu as b,ju as c,Ai as d,sl as e,Uo as f,Ae as g,ha as h,ms as i,Hl as j,K as k,Ts as l,gd as m,br as n,Er as o,Zc as p,md as q,vd as r,yd as s,Rd as t,Ed as u,Td as v,Cd as w,Id as x,Ad as y,Md as z,Dd as A,Od as B};
