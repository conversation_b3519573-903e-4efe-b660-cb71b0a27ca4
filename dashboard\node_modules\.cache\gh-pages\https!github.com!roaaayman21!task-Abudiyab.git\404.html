<!doctype html>
<html lang="en" data-critters-container>
<head><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <meta charset="utf-8">
  <title>Dashboard - User Management</title>
  <base href="C:/Program Files/Git/task-Abudiyab/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 32 32%22><defs><linearGradient id=%22grad%22 x1=%220%25%22 y1=%220%25%22 x2=%22100%25%22 y2=%22100%25%22><stop offset=%220%25%22 stop-color=%22%236366f1%22/><stop offset=%22100%25%22 stop-color=%22%238b5cf6%22/></linearGradient></defs><rect width=%2232%22 height=%2232%22 rx=%228%22 fill=%22url(%23grad)%22/><rect x=%226%22 y=%2210%22 width=%2220%22 height=%223%22 rx=%221.5%22 fill=%22white%22/><rect x=%226%22 y=%2215%22 width=%2215%22 height=%222%22 rx=%221%22 fill=%22white%22 opacity=%220.8%22/><rect x=%226%22 y=%2219%22 width=%2212%22 height=%222%22 rx=%221%22 fill=%22white%22 opacity=%220.6%22/></svg>">
  <style>@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:300;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:400;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3GUBGEe.woff2) format('woff2');unicode-range:U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3iUBGEe.woff2) format('woff2');unicode-range:U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3CUBGEe.woff2) format('woff2');unicode-range:U+1F00-1FFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3-UBGEe.woff2) format('woff2');unicode-range:U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMawCUBGEe.woff2) format('woff2');unicode-range:U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMaxKUBGEe.woff2) format('woff2');unicode-range:U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3OUBGEe.woff2) format('woff2');unicode-range:U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3KUBGEe.woff2) format('woff2');unicode-range:U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}@font-face{font-family:'Roboto';font-style:normal;font-weight:500;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/roboto/v48/KFO7CnqEu92Fr1ME7kSn66aGLdTylUAMa3yUBA.woff2) format('woff2');unicode-range:U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}</style>
  <style>@font-face{font-family:'Material Icons';font-style:normal;font-weight:400;src:url(https://fonts.gstatic.com/s/materialicons/v143/flUhRq6tzZclQEJ-Vdg-IuiaDsNc.woff2) format('woff2');}.material-icons{font-family:'Material Icons';font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-feature-settings:'liga';-webkit-font-smoothing:antialiased;}</style>
<style>html{--mat-app-background-color:#faf9fd;--mat-app-text-color:#1a1b1f;--mat-app-elevation-shadow-level-0:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-1:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-2:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-3:0px 3px 3px -2px rgba(0, 0, 0, .2), 0px 3px 4px 0px rgba(0, 0, 0, .14), 0px 1px 8px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-4:0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-5:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 5px 8px 0px rgba(0, 0, 0, .14), 0px 1px 14px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-6:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-7:0px 4px 5px -2px rgba(0, 0, 0, .2), 0px 7px 10px 1px rgba(0, 0, 0, .14), 0px 2px 16px 1px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-8:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-9:0px 5px 6px -3px rgba(0, 0, 0, .2), 0px 9px 12px 1px rgba(0, 0, 0, .14), 0px 3px 16px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-10:0px 6px 6px -3px rgba(0, 0, 0, .2), 0px 10px 14px 1px rgba(0, 0, 0, .14), 0px 4px 18px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-11:0px 6px 7px -4px rgba(0, 0, 0, .2), 0px 11px 15px 1px rgba(0, 0, 0, .14), 0px 4px 20px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-12:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-13:0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 13px 19px 2px rgba(0, 0, 0, .14), 0px 5px 24px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-14:0px 7px 9px -4px rgba(0, 0, 0, .2), 0px 14px 21px 2px rgba(0, 0, 0, .14), 0px 5px 26px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-15:0px 8px 9px -5px rgba(0, 0, 0, .2), 0px 15px 22px 2px rgba(0, 0, 0, .14), 0px 6px 28px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-16:0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-17:0px 8px 11px -5px rgba(0, 0, 0, .2), 0px 17px 26px 2px rgba(0, 0, 0, .14), 0px 6px 32px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-18:0px 9px 11px -5px rgba(0, 0, 0, .2), 0px 18px 28px 2px rgba(0, 0, 0, .14), 0px 7px 34px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-19:0px 9px 12px -6px rgba(0, 0, 0, .2), 0px 19px 29px 2px rgba(0, 0, 0, .14), 0px 7px 36px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-20:0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 20px 31px 3px rgba(0, 0, 0, .14), 0px 8px 38px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-21:0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 21px 33px 3px rgba(0, 0, 0, .14), 0px 8px 40px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-22:0px 10px 14px -6px rgba(0, 0, 0, .2), 0px 22px 35px 3px rgba(0, 0, 0, .14), 0px 8px 42px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-23:0px 11px 14px -7px rgba(0, 0, 0, .2), 0px 23px 36px 3px rgba(0, 0, 0, .14), 0px 9px 44px 8px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-24:0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12);--mat-ripple-color:rgba(26, 27, 31, .1);--mat-option-selected-state-label-text-color:#131c2b;--mat-option-label-text-color:#1a1b1f;--mat-option-hover-state-layer-color:rgba(26, 27, 31, .08);--mat-option-focus-state-layer-color:rgba(26, 27, 31, .12);--mat-option-selected-state-layer-color:#dae2f9;--mat-option-label-text-font:Roboto, sans-serif;--mat-option-label-text-line-height:1.25rem;--mat-option-label-text-size:1rem;--mat-option-label-text-tracking:.006rem;--mat-option-label-text-weight:400;--mat-optgroup-label-text-color:#44474e;--mat-optgroup-label-text-font:Roboto, sans-serif;--mat-optgroup-label-text-line-height:1.25rem;--mat-optgroup-label-text-size:.875rem;--mat-optgroup-label-text-tracking:.006rem;--mat-optgroup-label-text-weight:500;--mat-full-pseudo-checkbox-selected-icon-color:#005cbb;--mat-full-pseudo-checkbox-selected-checkmark-color:#ffffff;--mat-full-pseudo-checkbox-unselected-icon-color:#44474e;--mat-full-pseudo-checkbox-disabled-selected-checkmark-color:#faf9fd;--mat-full-pseudo-checkbox-disabled-unselected-icon-color:rgba(26, 27, 31, .38);--mat-full-pseudo-checkbox-disabled-selected-icon-color:rgba(26, 27, 31, .38);--mat-minimal-pseudo-checkbox-selected-checkmark-color:#005cbb;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color:rgba(26, 27, 31, .38);--mdc-elevated-card-container-color:#f4f3f6;--mdc-elevated-card-container-elevation:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-elevated-card-container-shape:12px;--mdc-outlined-card-container-color:#faf9fd;--mdc-outlined-card-outline-color:#c4c6d0;--mdc-outlined-card-container-elevation:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mdc-outlined-card-container-shape:12px;--mdc-outlined-card-outline-width:1px;--mat-card-subtitle-text-color:#1a1b1f;--mat-card-title-text-font:Roboto, sans-serif;--mat-card-title-text-line-height:1.75rem;--mat-card-title-text-size:1.375rem;--mat-card-title-text-tracking:0;--mat-card-title-text-weight:400;--mat-card-subtitle-text-font:Roboto, sans-serif;--mat-card-subtitle-text-line-height:1.5rem;--mat-card-subtitle-text-size:1rem;--mat-card-subtitle-text-tracking:.009rem;--mat-card-subtitle-text-weight:500;--mdc-linear-progress-active-indicator-color:#005cbb;--mdc-linear-progress-track-color:#e0e2ec;--mdc-linear-progress-active-indicator-height:4px;--mdc-linear-progress-track-height:4px;--mdc-linear-progress-track-shape:0;--mdc-plain-tooltip-container-color:#2f3033;--mdc-plain-tooltip-supporting-text-color:#f2f0f4;--mdc-plain-tooltip-supporting-text-line-height:1rem;--mdc-plain-tooltip-supporting-text-font:Roboto, sans-serif;--mdc-plain-tooltip-supporting-text-size:.75rem;--mdc-plain-tooltip-supporting-text-weight:400;--mdc-plain-tooltip-supporting-text-tracking:.025rem;--mdc-plain-tooltip-container-shape:4px;--mdc-filled-text-field-caret-color:#005cbb;--mdc-filled-text-field-focus-active-indicator-color:#005cbb;--mdc-filled-text-field-focus-label-text-color:#005cbb;--mdc-filled-text-field-container-color:#e0e2ec;--mdc-filled-text-field-disabled-container-color:rgba(26, 27, 31, .04);--mdc-filled-text-field-label-text-color:#44474e;--mdc-filled-text-field-hover-label-text-color:#44474e;--mdc-filled-text-field-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-filled-text-field-input-text-color:#1a1b1f;--mdc-filled-text-field-disabled-input-text-color:rgba(26, 27, 31, .38);--mdc-filled-text-field-input-text-placeholder-color:#44474e;--mdc-filled-text-field-error-hover-label-text-color:#410002;--mdc-filled-text-field-error-focus-label-text-color:#ba1a1a;--mdc-filled-text-field-error-label-text-color:#ba1a1a;--mdc-filled-text-field-active-indicator-color:#44474e;--mdc-filled-text-field-disabled-active-indicator-color:rgba(26, 27, 31, .38);--mdc-filled-text-field-hover-active-indicator-color:#1a1b1f;--mdc-filled-text-field-error-active-indicator-color:#ba1a1a;--mdc-filled-text-field-error-focus-active-indicator-color:#ba1a1a;--mdc-filled-text-field-error-hover-active-indicator-color:#410002;--mdc-filled-text-field-label-text-font:Roboto, sans-serif;--mdc-filled-text-field-label-text-size:1rem;--mdc-filled-text-field-label-text-tracking:.031rem;--mdc-filled-text-field-label-text-weight:400;--mdc-filled-text-field-active-indicator-height:1px;--mdc-filled-text-field-focus-active-indicator-height:2px;--mdc-filled-text-field-container-shape:4px;--mdc-outlined-text-field-caret-color:#005cbb;--mdc-outlined-text-field-focus-outline-color:#005cbb;--mdc-outlined-text-field-focus-label-text-color:#005cbb;--mdc-outlined-text-field-label-text-color:#44474e;--mdc-outlined-text-field-hover-label-text-color:#1a1b1f;--mdc-outlined-text-field-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-outlined-text-field-input-text-color:#1a1b1f;--mdc-outlined-text-field-disabled-input-text-color:rgba(26, 27, 31, .38);--mdc-outlined-text-field-input-text-placeholder-color:#44474e;--mdc-outlined-text-field-error-focus-label-text-color:#ba1a1a;--mdc-outlined-text-field-error-label-text-color:#ba1a1a;--mdc-outlined-text-field-error-hover-label-text-color:#410002;--mdc-outlined-text-field-outline-color:#74777f;--mdc-outlined-text-field-disabled-outline-color:rgba(26, 27, 31, .12);--mdc-outlined-text-field-hover-outline-color:#1a1b1f;--mdc-outlined-text-field-error-focus-outline-color:#ba1a1a;--mdc-outlined-text-field-error-hover-outline-color:#410002;--mdc-outlined-text-field-error-outline-color:#ba1a1a;--mdc-outlined-text-field-label-text-font:Roboto, sans-serif;--mdc-outlined-text-field-label-text-size:1rem;--mdc-outlined-text-field-label-text-tracking:.031rem;--mdc-outlined-text-field-label-text-weight:400;--mdc-outlined-text-field-outline-width:1px;--mdc-outlined-text-field-focus-outline-width:2px;--mdc-outlined-text-field-container-shape:4px;--mat-form-field-focus-select-arrow-color:#005cbb;--mat-form-field-disabled-input-text-placeholder-color:rgba(26, 27, 31, .38);--mat-form-field-state-layer-color:#1a1b1f;--mat-form-field-error-text-color:#ba1a1a;--mat-form-field-select-option-text-color:#1a1b1f;--mat-form-field-select-disabled-option-text-color:rgba(26, 27, 31, .38);--mat-form-field-leading-icon-color:#44474e;--mat-form-field-disabled-leading-icon-color:rgba(26, 27, 31, .38);--mat-form-field-trailing-icon-color:#44474e;--mat-form-field-disabled-trailing-icon-color:rgba(26, 27, 31, .38);--mat-form-field-error-focus-trailing-icon-color:#ba1a1a;--mat-form-field-error-hover-trailing-icon-color:#410002;--mat-form-field-error-trailing-icon-color:#ba1a1a;--mat-form-field-enabled-select-arrow-color:#44474e;--mat-form-field-disabled-select-arrow-color:rgba(26, 27, 31, .38);--mat-form-field-hover-state-layer-opacity:.08;--mat-form-field-container-text-font:Roboto, sans-serif;--mat-form-field-container-text-line-height:1.5rem;--mat-form-field-container-text-size:1rem;--mat-form-field-container-text-tracking:.031rem;--mat-form-field-container-text-weight:400;--mat-form-field-subscript-text-font:Roboto, sans-serif;--mat-form-field-subscript-text-line-height:1rem;--mat-form-field-subscript-text-size:.75rem;--mat-form-field-subscript-text-tracking:.025rem;--mat-form-field-subscript-text-weight:400;--mat-form-field-container-height:56px;--mat-form-field-filled-label-display:block;--mat-form-field-container-vertical-padding:16px;--mat-form-field-filled-with-label-container-padding-top:24px;--mat-form-field-filled-with-label-container-padding-bottom:8px;--mat-form-field-focus-state-layer-opacity:0;--mat-select-panel-background-color:#efedf0;--mat-select-enabled-trigger-text-color:#1a1b1f;--mat-select-disabled-trigger-text-color:rgba(26, 27, 31, .38);--mat-select-placeholder-text-color:#44474e;--mat-select-enabled-arrow-color:#44474e;--mat-select-disabled-arrow-color:rgba(26, 27, 31, .38);--mat-select-focused-arrow-color:#005cbb;--mat-select-invalid-arrow-color:#ba1a1a;--mat-select-trigger-text-font:Roboto, sans-serif;--mat-select-trigger-text-line-height:1.5rem;--mat-select-trigger-text-size:1rem;--mat-select-trigger-text-tracking:.031rem;--mat-select-trigger-text-weight:400;--mat-select-arrow-transform:translateY(-8px);--mat-select-container-elevation-shadow:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-autocomplete-background-color:#efedf0;--mat-autocomplete-container-shape:4px;--mat-autocomplete-container-elevation-shadow:0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mdc-dialog-container-color:#faf9fd;--mdc-dialog-subhead-color:#1a1b1f;--mdc-dialog-supporting-text-color:#44474e;--mdc-dialog-subhead-font:Roboto, sans-serif;--mdc-dialog-subhead-line-height:2rem;--mdc-dialog-subhead-size:1.5rem;--mdc-dialog-subhead-weight:400;--mdc-dialog-subhead-tracking:0;--mdc-dialog-supporting-text-font:Roboto, sans-serif;--mdc-dialog-supporting-text-line-height:1.25rem;--mdc-dialog-supporting-text-size:.875rem;--mdc-dialog-supporting-text-weight:400;--mdc-dialog-supporting-text-tracking:.016rem;--mdc-dialog-container-shape:28px;--mat-dialog-container-elevation-shadow:none;--mat-dialog-container-max-width:560px;--mat-dialog-container-small-max-width:calc(100vw - 32px) ;--mat-dialog-container-min-width:280px;--mat-dialog-actions-alignment:flex-end;--mat-dialog-actions-padding:16px 24px;--mat-dialog-content-padding:20px 24px;--mat-dialog-with-actions-content-padding:20px 24px 0;--mat-dialog-headline-padding:6px 24px 13px;--mdc-chip-outline-color:#74777f;--mdc-chip-disabled-outline-color:rgba(26, 27, 31, .12);--mdc-chip-focus-outline-color:#44474e;--mdc-chip-hover-state-layer-opacity:.08;--mdc-chip-selected-hover-state-layer-opacity:.08;--mdc-chip-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-chip-elevated-selected-container-color:#dae2f9;--mdc-chip-flat-disabled-selected-container-color:rgba(26, 27, 31, .12);--mdc-chip-focus-state-layer-color:#44474e;--mdc-chip-hover-state-layer-color:#44474e;--mdc-chip-selected-hover-state-layer-color:#131c2b;--mdc-chip-focus-state-layer-opacity:.12;--mdc-chip-selected-focus-state-layer-color:#131c2b;--mdc-chip-selected-focus-state-layer-opacity:.12;--mdc-chip-label-text-color:#44474e;--mdc-chip-selected-label-text-color:#131c2b;--mdc-chip-with-icon-icon-color:#44474e;--mdc-chip-with-icon-disabled-icon-color:#1a1b1f;--mdc-chip-with-icon-selected-icon-color:#131c2b;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color:#1a1b1f;--mdc-chip-with-trailing-icon-trailing-icon-color:#44474e;--mdc-chip-label-text-font:Roboto, sans-serif;--mdc-chip-label-text-line-height:1.25rem;--mdc-chip-label-text-size:.875rem;--mdc-chip-label-text-tracking:.006rem;--mdc-chip-label-text-weight:500;--mdc-chip-container-height:32px;--mdc-chip-container-shape-radius:8px;--mdc-chip-with-avatar-avatar-shape-radius:24px;--mdc-chip-with-avatar-avatar-size:24px;--mdc-chip-with-icon-icon-size:18px;--mdc-chip-outline-width:1px;--mdc-chip-with-avatar-disabled-avatar-opacity:.38;--mdc-chip-flat-selected-outline-width:0;--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity:.38;--mdc-chip-with-icon-disabled-icon-opacity:.38;--mdc-chip-elevated-container-color:transparent;--mat-chip-trailing-action-state-layer-color:#44474e;--mat-chip-selected-trailing-action-state-layer-color:#131c2b;--mat-chip-trailing-action-hover-state-layer-opacity:.08;--mat-chip-trailing-action-focus-state-layer-opacity:.12;--mat-chip-selected-disabled-trailing-icon-color:#1a1b1f;--mat-chip-selected-trailing-icon-color:#131c2b;--mat-chip-disabled-container-opacity:1;--mat-chip-trailing-action-opacity:1;--mat-chip-trailing-action-focus-opacity:1;--mdc-switch-selected-focus-state-layer-opacity:.12;--mdc-switch-selected-hover-state-layer-opacity:.08;--mdc-switch-selected-pressed-state-layer-opacity:.12;--mdc-switch-unselected-focus-state-layer-opacity:.12;--mdc-switch-unselected-hover-state-layer-opacity:.08;--mdc-switch-unselected-pressed-state-layer-opacity:.12;--mdc-switch-selected-focus-state-layer-color:#005cbb;--mdc-switch-selected-handle-color:#ffffff;--mdc-switch-selected-hover-state-layer-color:#005cbb;--mdc-switch-selected-pressed-state-layer-color:#005cbb;--mdc-switch-selected-focus-handle-color:#d7e3ff;--mdc-switch-selected-hover-handle-color:#d7e3ff;--mdc-switch-selected-pressed-handle-color:#d7e3ff;--mdc-switch-selected-focus-track-color:#005cbb;--mdc-switch-selected-hover-track-color:#005cbb;--mdc-switch-selected-pressed-track-color:#005cbb;--mdc-switch-selected-track-color:#005cbb;--mdc-switch-disabled-selected-handle-color:#faf9fd;--mdc-switch-disabled-selected-icon-color:#1a1b1f;--mdc-switch-disabled-selected-track-color:#1a1b1f;--mdc-switch-disabled-unselected-handle-color:#1a1b1f;--mdc-switch-disabled-unselected-icon-color:#e0e2ec;--mdc-switch-disabled-unselected-track-color:#e0e2ec;--mdc-switch-selected-icon-color:#001b3f;--mdc-switch-unselected-focus-handle-color:#44474e;--mdc-switch-unselected-focus-state-layer-color:#1a1b1f;--mdc-switch-unselected-focus-track-color:#e0e2ec;--mdc-switch-unselected-handle-color:#74777f;--mdc-switch-unselected-hover-handle-color:#44474e;--mdc-switch-unselected-hover-state-layer-color:#1a1b1f;--mdc-switch-unselected-hover-track-color:#e0e2ec;--mdc-switch-unselected-icon-color:#e0e2ec;--mdc-switch-unselected-pressed-handle-color:#44474e;--mdc-switch-unselected-pressed-state-layer-color:#1a1b1f;--mdc-switch-unselected-pressed-track-color:#e0e2ec;--mdc-switch-unselected-track-color:#e0e2ec;--mdc-switch-disabled-selected-icon-opacity:.38;--mdc-switch-disabled-track-opacity:.12;--mdc-switch-disabled-unselected-icon-opacity:.38;--mdc-switch-handle-shape:9999px;--mdc-switch-selected-icon-size:16px;--mdc-switch-track-height:32px;--mdc-switch-track-shape:9999px;--mdc-switch-track-width:52px;--mdc-switch-unselected-icon-size:16px;--mdc-switch-state-layer-size:40px;--mat-switch-track-outline-color:#74777f;--mat-switch-disabled-unselected-track-outline-color:#1a1b1f;--mat-switch-label-text-color:#1a1b1f;--mat-switch-label-text-font:Roboto, sans-serif;--mat-switch-label-text-line-height:1.25rem;--mat-switch-label-text-size:.875rem;--mat-switch-label-text-tracking:.016rem;--mat-switch-label-text-weight:400;--mat-switch-disabled-selected-handle-opacity:1;--mat-switch-disabled-unselected-handle-opacity:.38;--mat-switch-unselected-handle-size:16px;--mat-switch-selected-handle-size:24px;--mat-switch-pressed-handle-size:28px;--mat-switch-with-icon-handle-size:24px;--mat-switch-selected-handle-horizontal-margin:0 24px;--mat-switch-selected-with-icon-handle-horizontal-margin:0 24px;--mat-switch-selected-pressed-handle-horizontal-margin:0 22px;--mat-switch-unselected-handle-horizontal-margin:0 8px;--mat-switch-unselected-with-icon-handle-horizontal-margin:0 4px;--mat-switch-unselected-pressed-handle-horizontal-margin:0 2px;--mat-switch-visible-track-opacity:1;--mat-switch-hidden-track-opacity:0;--mat-switch-visible-track-transition:opacity 75ms;--mat-switch-hidden-track-transition:opacity 75ms;--mat-switch-track-outline-width:2px;--mat-switch-selected-track-outline-width:2px;--mat-switch-selected-track-outline-color:transparent;--mat-switch-disabled-unselected-track-outline-width:2px;--mdc-radio-disabled-selected-icon-color:#1a1b1f;--mdc-radio-disabled-unselected-icon-color:#1a1b1f;--mdc-radio-unselected-hover-icon-color:#1a1b1f;--mdc-radio-unselected-focus-icon-color:#1a1b1f;--mdc-radio-unselected-icon-color:#44474e;--mdc-radio-unselected-pressed-icon-color:#1a1b1f;--mdc-radio-selected-focus-icon-color:#005cbb;--mdc-radio-selected-hover-icon-color:#005cbb;--mdc-radio-selected-icon-color:#005cbb;--mdc-radio-selected-pressed-icon-color:#005cbb;--mdc-radio-state-layer-size:40px;--mdc-radio-disabled-selected-icon-opacity:.38;--mdc-radio-disabled-unselected-icon-opacity:.38;--mat-radio-ripple-color:#1a1b1f;--mat-radio-checked-ripple-color:#005cbb;--mat-radio-disabled-label-color:rgba(26, 27, 31, .38);--mat-radio-label-text-color:#1a1b1f;--mat-radio-label-text-font:Roboto, sans-serif;--mat-radio-label-text-line-height:1.25rem;--mat-radio-label-text-size:.875rem;--mat-radio-label-text-tracking:.016rem;--mat-radio-label-text-weight:400;--mat-radio-touch-target-display:block;--mdc-slider-handle-elevation:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-slider-handle-color:#005cbb;--mdc-slider-focus-handle-color:#005cbb;--mdc-slider-hover-handle-color:#005cbb;--mdc-slider-active-track-color:#005cbb;--mdc-slider-inactive-track-color:#e0e2ec;--mdc-slider-with-tick-marks-inactive-container-color:#44474e;--mdc-slider-with-tick-marks-active-container-color:#ffffff;--mdc-slider-disabled-active-track-color:#1a1b1f;--mdc-slider-disabled-handle-color:#1a1b1f;--mdc-slider-disabled-inactive-track-color:#1a1b1f;--mdc-slider-label-container-color:#005cbb;--mdc-slider-label-label-text-color:#ffffff;--mdc-slider-with-overlap-handle-outline-color:#ffffff;--mdc-slider-with-tick-marks-disabled-container-color:#1a1b1f;--mdc-slider-label-label-text-font:Roboto, sans-serif;--mdc-slider-label-label-text-size:.75rem;--mdc-slider-label-label-text-line-height:1rem;--mdc-slider-label-label-text-tracking:.031rem;--mdc-slider-label-label-text-weight:500;--mdc-slider-active-track-height:4px;--mdc-slider-active-track-shape:9999px;--mdc-slider-handle-height:20px;--mdc-slider-handle-shape:9999px;--mdc-slider-handle-width:20px;--mdc-slider-inactive-track-height:4px;--mdc-slider-inactive-track-shape:9999px;--mdc-slider-with-overlap-handle-outline-width:1px;--mdc-slider-with-tick-marks-active-container-opacity:.38;--mdc-slider-with-tick-marks-container-shape:9999px;--mdc-slider-with-tick-marks-container-size:2px;--mdc-slider-with-tick-marks-inactive-container-opacity:.38;--mat-slider-ripple-color:#005cbb;--mat-slider-hover-state-layer-color:rgba(0, 92, 187, .05);--mat-slider-focus-state-layer-color:rgba(0, 92, 187, .2);--mat-slider-value-indicator-width:28px;--mat-slider-value-indicator-height:28px;--mat-slider-value-indicator-caret-display:none;--mat-slider-value-indicator-border-radius:50% 50% 50% 0;--mat-slider-value-indicator-padding:0;--mat-slider-value-indicator-text-transform:rotate(45deg);--mat-slider-value-indicator-container-transform:translateX(-50%) rotate(-45deg);--mat-slider-value-indicator-opacity:1;--mat-menu-item-label-text-color:#1a1b1f;--mat-menu-item-icon-color:#44474e;--mat-menu-item-hover-state-layer-color:rgba(26, 27, 31, .08);--mat-menu-item-focus-state-layer-color:rgba(26, 27, 31, .12);--mat-menu-container-color:#efedf0;--mat-menu-divider-color:#e0e2ec;--mat-menu-item-label-text-font:Roboto, sans-serif;--mat-menu-item-label-text-size:.875rem;--mat-menu-item-label-text-tracking:.006rem;--mat-menu-item-label-text-line-height:1.25rem;--mat-menu-item-label-text-weight:500;--mat-menu-container-shape:4px;--mat-menu-divider-bottom-spacing:8px;--mat-menu-divider-top-spacing:8px;--mat-menu-item-spacing:12px;--mat-menu-item-icon-size:24px;--mat-menu-item-leading-spacing:12px;--mat-menu-item-trailing-spacing:12px;--mat-menu-item-with-icon-leading-spacing:12px;--mat-menu-item-with-icon-trailing-spacing:12px;--mat-menu-base-elevation-level:2;--mdc-list-list-item-container-color:transparent;--mdc-list-list-item-leading-avatar-color:#d7e3ff;--mdc-list-list-item-disabled-state-layer-color:#1a1b1f;--mdc-list-list-item-disabled-state-layer-opacity:.12;--mdc-list-list-item-label-text-color:#1a1b1f;--mdc-list-list-item-supporting-text-color:#44474e;--mdc-list-list-item-leading-icon-color:#44474e;--mdc-list-list-item-trailing-supporting-text-color:#44474e;--mdc-list-list-item-trailing-icon-color:#44474e;--mdc-list-list-item-selected-trailing-icon-color:#005cbb;--mdc-list-list-item-disabled-label-text-color:#1a1b1f;--mdc-list-list-item-disabled-leading-icon-color:#1a1b1f;--mdc-list-list-item-disabled-trailing-icon-color:#1a1b1f;--mdc-list-list-item-hover-label-text-color:#1a1b1f;--mdc-list-list-item-focus-label-text-color:#1a1b1f;--mdc-list-list-item-hover-state-layer-color:#1a1b1f;--mdc-list-list-item-hover-state-layer-opacity:.08;--mdc-list-list-item-focus-state-layer-color:#1a1b1f;--mdc-list-list-item-focus-state-layer-opacity:.12;--mdc-list-list-item-label-text-font:Roboto, sans-serif;--mdc-list-list-item-label-text-line-height:1.5rem;--mdc-list-list-item-label-text-size:1rem;--mdc-list-list-item-label-text-tracking:.031rem;--mdc-list-list-item-label-text-weight:400;--mdc-list-list-item-supporting-text-font:Roboto, sans-serif;--mdc-list-list-item-supporting-text-line-height:1.25rem;--mdc-list-list-item-supporting-text-size:.875rem;--mdc-list-list-item-supporting-text-tracking:.016rem;--mdc-list-list-item-supporting-text-weight:400;--mdc-list-list-item-trailing-supporting-text-font:Roboto, sans-serif;--mdc-list-list-item-trailing-supporting-text-line-height:1rem;--mdc-list-list-item-trailing-supporting-text-size:.688rem;--mdc-list-list-item-trailing-supporting-text-tracking:.031rem;--mdc-list-list-item-trailing-supporting-text-weight:500;--mdc-list-list-item-one-line-container-height:48px;--mdc-list-list-item-two-line-container-height:64px;--mdc-list-list-item-three-line-container-height:88px;--mdc-list-list-item-container-shape:0;--mdc-list-list-item-leading-avatar-shape:9999px;--mdc-list-list-item-leading-icon-size:24px;--mdc-list-list-item-leading-avatar-size:40px;--mdc-list-list-item-trailing-icon-size:24px;--mdc-list-list-item-disabled-label-text-opacity:.3;--mdc-list-list-item-disabled-leading-icon-opacity:.38;--mdc-list-list-item-disabled-trailing-icon-opacity:.38;--mat-list-active-indicator-color:#dae2f9;--mat-list-list-item-leading-icon-start-space:16px;--mat-list-list-item-leading-icon-end-space:16px;--mat-list-active-indicator-shape:9999px;--mat-paginator-container-text-color:#1a1b1f;--mat-paginator-container-background-color:#faf9fd;--mat-paginator-enabled-icon-color:#44474e;--mat-paginator-disabled-icon-color:rgba(26, 27, 31, .38);--mat-paginator-container-text-font:Roboto, sans-serif;--mat-paginator-container-text-line-height:1rem;--mat-paginator-container-text-size:.75rem;--mat-paginator-container-text-tracking:.025rem;--mat-paginator-container-text-weight:400;--mat-paginator-select-trigger-text-size:.75rem;--mat-paginator-container-size:56px;--mat-paginator-form-field-container-height:40px;--mat-paginator-form-field-container-vertical-padding:8px;--mat-paginator-touch-target-display:block;--mdc-secondary-navigation-tab-container-height:48px;--mdc-tab-indicator-active-indicator-color:#005cbb;--mdc-tab-indicator-active-indicator-height:2px;--mdc-tab-indicator-active-indicator-shape:0;--mat-tab-header-divider-color:#e0e2ec;--mat-tab-header-pagination-icon-color:#1a1b1f;--mat-tab-header-inactive-label-text-color:#1a1b1f;--mat-tab-header-active-label-text-color:#1a1b1f;--mat-tab-header-active-ripple-color:#1a1b1f;--mat-tab-header-inactive-ripple-color:#1a1b1f;--mat-tab-header-inactive-focus-label-text-color:#1a1b1f;--mat-tab-header-inactive-hover-label-text-color:#1a1b1f;--mat-tab-header-active-focus-label-text-color:#1a1b1f;--mat-tab-header-active-hover-label-text-color:#1a1b1f;--mat-tab-header-active-focus-indicator-color:#005cbb;--mat-tab-header-active-hover-indicator-color:#005cbb;--mat-tab-header-label-text-font:Roboto, sans-serif;--mat-tab-header-label-text-size:.875rem;--mat-tab-header-label-text-tracking:.006rem;--mat-tab-header-label-text-line-height:1.25rem;--mat-tab-header-label-text-weight:500;--mat-tab-header-divider-height:1px;--mdc-checkbox-disabled-selected-checkmark-color:#faf9fd;--mdc-checkbox-selected-focus-state-layer-opacity:.12;--mdc-checkbox-selected-hover-state-layer-opacity:.08;--mdc-checkbox-selected-pressed-state-layer-opacity:.12;--mdc-checkbox-unselected-focus-state-layer-opacity:.12;--mdc-checkbox-unselected-hover-state-layer-opacity:.08;--mdc-checkbox-unselected-pressed-state-layer-opacity:.12;--mdc-checkbox-selected-pressed-icon-color:#005cbb;--mdc-checkbox-disabled-selected-icon-color:rgba(26, 27, 31, .38);--mdc-checkbox-disabled-unselected-icon-color:rgba(26, 27, 31, .38);--mdc-checkbox-selected-checkmark-color:#ffffff;--mdc-checkbox-selected-focus-icon-color:#005cbb;--mdc-checkbox-selected-hover-icon-color:#005cbb;--mdc-checkbox-selected-icon-color:#005cbb;--mdc-checkbox-unselected-focus-icon-color:#1a1b1f;--mdc-checkbox-unselected-hover-icon-color:#1a1b1f;--mdc-checkbox-unselected-icon-color:#44474e;--mdc-checkbox-selected-focus-state-layer-color:#005cbb;--mdc-checkbox-selected-hover-state-layer-color:#005cbb;--mdc-checkbox-selected-pressed-state-layer-color:#1a1b1f;--mdc-checkbox-unselected-focus-state-layer-color:#1a1b1f;--mdc-checkbox-unselected-hover-state-layer-color:#1a1b1f;--mdc-checkbox-unselected-pressed-state-layer-color:#005cbb;--mdc-checkbox-state-layer-size:40px;--mat-checkbox-disabled-label-color:rgba(26, 27, 31, .38);--mat-checkbox-label-text-color:#1a1b1f;--mat-checkbox-label-text-font:Roboto, sans-serif;--mat-checkbox-label-text-line-height:1.25rem;--mat-checkbox-label-text-size:.875rem;--mat-checkbox-label-text-tracking:.016rem;--mat-checkbox-label-text-weight:400;--mat-checkbox-touch-target-display:block;--mdc-text-button-label-text-color:#005cbb;--mdc-text-button-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-text-button-label-text-font:Roboto, sans-serif;--mdc-text-button-label-text-size:.875rem;--mdc-text-button-label-text-tracking:.006rem;--mdc-text-button-label-text-weight:500;--mdc-text-button-container-height:40px;--mdc-text-button-container-shape:9999px;--mdc-protected-button-container-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-protected-button-disabled-container-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mdc-protected-button-focus-container-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-protected-button-hover-container-elevation-shadow:0px 3px 3px -2px rgba(0, 0, 0, .2), 0px 3px 4px 0px rgba(0, 0, 0, .14), 0px 1px 8px 0px rgba(0, 0, 0, .12);--mdc-protected-button-pressed-container-elevation-shadow:0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-protected-button-container-color:#faf9fd;--mdc-protected-button-label-text-color:#005cbb;--mdc-protected-button-disabled-container-color:rgba(26, 27, 31, .12);--mdc-protected-button-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-protected-button-label-text-font:Roboto, sans-serif;--mdc-protected-button-label-text-size:.875rem;--mdc-protected-button-label-text-tracking:.006rem;--mdc-protected-button-label-text-weight:500;--mdc-protected-button-container-height:40px;--mdc-protected-button-container-shape:9999px;--mdc-filled-button-container-color:#005cbb;--mdc-filled-button-label-text-color:#ffffff;--mdc-filled-button-disabled-container-color:rgba(26, 27, 31, .12);--mdc-filled-button-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-filled-button-label-text-font:Roboto, sans-serif;--mdc-filled-button-label-text-size:.875rem;--mdc-filled-button-label-text-tracking:.006rem;--mdc-filled-button-label-text-weight:500;--mdc-filled-button-container-height:40px;--mdc-filled-button-container-shape:9999px;--mdc-outlined-button-disabled-outline-color:rgba(26, 27, 31, .12);--mdc-outlined-button-disabled-label-text-color:rgba(26, 27, 31, .38);--mdc-outlined-button-label-text-color:#005cbb;--mdc-outlined-button-outline-color:#74777f;--mdc-outlined-button-label-text-font:Roboto, sans-serif;--mdc-outlined-button-label-text-size:.875rem;--mdc-outlined-button-label-text-tracking:.006rem;--mdc-outlined-button-label-text-weight:500;--mdc-outlined-button-container-height:40px;--mdc-outlined-button-outline-width:1px;--mdc-outlined-button-container-shape:9999px;--mat-text-button-state-layer-color:#005cbb;--mat-text-button-disabled-state-layer-color:#44474e;--mat-text-button-ripple-color:rgba(0, 92, 187, .12);--mat-text-button-hover-state-layer-opacity:.08;--mat-text-button-focus-state-layer-opacity:.12;--mat-text-button-pressed-state-layer-opacity:.12;--mat-text-button-touch-target-display:block;--mat-text-button-horizontal-padding:12px;--mat-text-button-with-icon-horizontal-padding:16px;--mat-text-button-icon-spacing:8px;--mat-text-button-icon-offset:-4px;--mat-protected-button-state-layer-color:#005cbb;--mat-protected-button-disabled-state-layer-color:#44474e;--mat-protected-button-ripple-color:rgba(0, 92, 187, .12);--mat-protected-button-hover-state-layer-opacity:.08;--mat-protected-button-focus-state-layer-opacity:.12;--mat-protected-button-pressed-state-layer-opacity:.12;--mat-protected-button-touch-target-display:block;--mat-protected-button-horizontal-padding:24px;--mat-protected-button-icon-spacing:8px;--mat-protected-button-icon-offset:-8px;--mat-filled-button-state-layer-color:#ffffff;--mat-filled-button-disabled-state-layer-color:#44474e;--mat-filled-button-ripple-color:rgba(255, 255, 255, .12);--mat-filled-button-hover-state-layer-opacity:.08;--mat-filled-button-focus-state-layer-opacity:.12;--mat-filled-button-pressed-state-layer-opacity:.12;--mat-filled-button-touch-target-display:block;--mat-filled-button-horizontal-padding:24px;--mat-filled-button-icon-spacing:8px;--mat-filled-button-icon-offset:-8px;--mat-outlined-button-state-layer-color:#005cbb;--mat-outlined-button-disabled-state-layer-color:#44474e;--mat-outlined-button-ripple-color:rgba(0, 92, 187, .12);--mat-outlined-button-hover-state-layer-opacity:.08;--mat-outlined-button-focus-state-layer-opacity:.12;--mat-outlined-button-pressed-state-layer-opacity:.12;--mat-outlined-button-touch-target-display:block;--mat-outlined-button-horizontal-padding:24px;--mat-outlined-button-icon-spacing:8px;--mat-outlined-button-icon-offset:-8px;--mdc-icon-button-icon-color:#44474e;--mdc-icon-button-disabled-icon-color:rgba(26, 27, 31, .38);--mdc-icon-button-state-layer-size:40px;--mdc-icon-button-icon-size:24px;--mat-icon-button-state-layer-color:#44474e;--mat-icon-button-disabled-state-layer-color:#44474e;--mat-icon-button-ripple-color:rgba(68, 71, 78, .12);--mat-icon-button-hover-state-layer-opacity:.08;--mat-icon-button-focus-state-layer-opacity:.12;--mat-icon-button-pressed-state-layer-opacity:.12;--mat-icon-button-touch-target-display:block;--mdc-extended-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-extended-fab-focus-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-extended-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-extended-fab-pressed-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-extended-fab-label-text-font:Roboto, sans-serif;--mdc-extended-fab-label-text-size:.875rem;--mdc-extended-fab-label-text-tracking:.006rem;--mdc-extended-fab-label-text-weight:500;--mdc-extended-fab-container-height:56px;--mdc-extended-fab-container-shape:16px;--mdc-fab-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-focus-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-pressed-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-container-color:#d7e3ff;--mdc-fab-container-shape:16px;--mdc-fab-small-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-small-focus-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-small-hover-container-elevation-shadow:0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-small-pressed-container-elevation-shadow:0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-small-container-color:#d7e3ff;--mdc-fab-small-container-shape:12px;--mat-fab-foreground-color:#001b3f;--mat-fab-state-layer-color:#001b3f;--mat-fab-ripple-color:rgba(0, 27, 63, .12);--mat-fab-hover-state-layer-opacity:.08;--mat-fab-focus-state-layer-opacity:.12;--mat-fab-pressed-state-layer-opacity:.12;--mat-fab-disabled-state-container-color:rgba(26, 27, 31, .12);--mat-fab-disabled-state-foreground-color:rgba(26, 27, 31, .38);--mat-fab-touch-target-display:block;--mat-fab-small-foreground-color:#001b3f;--mat-fab-small-state-layer-color:#001b3f;--mat-fab-small-ripple-color:rgba(0, 27, 63, .12);--mat-fab-small-hover-state-layer-opacity:.08;--mat-fab-small-focus-state-layer-opacity:.12;--mat-fab-small-pressed-state-layer-opacity:.12;--mat-fab-small-disabled-state-container-color:rgba(26, 27, 31, .12);--mat-fab-small-disabled-state-foreground-color:rgba(26, 27, 31, .38);--mdc-snackbar-container-color:#2f3033;--mdc-snackbar-supporting-text-color:#f2f0f4;--mdc-snackbar-supporting-text-font:Roboto, sans-serif;--mdc-snackbar-supporting-text-line-height:1.25rem;--mdc-snackbar-supporting-text-size:.875rem;--mdc-snackbar-supporting-text-weight:400;--mdc-snackbar-container-shape:4px;--mat-snack-bar-button-color:#abc7ff;--mat-table-background-color:#faf9fd;--mat-table-header-headline-color:#1a1b1f;--mat-table-row-item-label-text-color:#1a1b1f;--mat-table-row-item-outline-color:#74777f;--mat-table-header-headline-font:Roboto, sans-serif;--mat-table-header-headline-line-height:1.25rem;--mat-table-header-headline-size:.875rem;--mat-table-header-headline-weight:500;--mat-table-header-headline-tracking:.006rem;--mat-table-row-item-label-text-font:Roboto, sans-serif;--mat-table-row-item-label-text-line-height:1.25rem;--mat-table-row-item-label-text-size:.875rem;--mat-table-row-item-label-text-weight:400;--mat-table-row-item-label-text-tracking:.016rem;--mat-table-footer-supporting-text-font:Roboto, sans-serif;--mat-table-footer-supporting-text-line-height:1.25rem;--mat-table-footer-supporting-text-size:.875rem;--mat-table-footer-supporting-text-weight:400;--mat-table-footer-supporting-text-tracking:.016rem;--mat-table-header-container-height:56px;--mat-table-footer-container-height:52px;--mat-table-row-item-container-height:52px;--mat-table-row-item-outline-width:1px;--mdc-circular-progress-active-indicator-color:#005cbb;--mdc-circular-progress-active-indicator-width:4px;--mdc-circular-progress-size:48px;--mat-badge-background-color:#ba1a1a;--mat-badge-text-color:#ffffff;--mat-badge-disabled-state-background-color:rgba(186, 26, 26, .38);--mat-badge-disabled-state-text-color:#ffffff;--mat-badge-text-font:Roboto, sans-serif;--mat-badge-text-size:.688rem;--mat-badge-text-weight:500;--mat-badge-large-size-text-size:.688rem;--mat-badge-container-shape:9999px;--mat-badge-container-size:16px;--mat-badge-small-size-container-size:6px;--mat-badge-large-size-container-size:16px;--mat-badge-legacy-container-size:unset;--mat-badge-legacy-small-size-container-size:unset;--mat-badge-legacy-large-size-container-size:unset;--mat-badge-container-offset:-12px 0;--mat-badge-small-size-container-offset:-6px 0;--mat-badge-large-size-container-offset:-12px 0;--mat-badge-container-overlap-offset:-12px;--mat-badge-small-size-container-overlap-offset:-6px;--mat-badge-large-size-container-overlap-offset:-12px;--mat-badge-container-padding:0 4px;--mat-badge-small-size-container-padding:0;--mat-badge-large-size-container-padding:0 4px;--mat-badge-line-height:16px;--mat-badge-small-size-text-size:0;--mat-badge-small-size-line-height:6px;--mat-badge-large-size-line-height:16px;--mat-bottom-sheet-container-text-color:#1a1b1f;--mat-bottom-sheet-container-background-color:#f4f3f6;--mat-bottom-sheet-container-text-font:Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height:1.5rem;--mat-bottom-sheet-container-text-size:1rem;--mat-bottom-sheet-container-text-tracking:.031rem;--mat-bottom-sheet-container-text-weight:400;--mat-bottom-sheet-container-shape:28px;--mat-standard-button-toggle-hover-state-layer-opacity:.08;--mat-standard-button-toggle-focus-state-layer-opacity:.12;--mat-standard-button-toggle-text-color:#1a1b1f;--mat-standard-button-toggle-state-layer-color:#1a1b1f;--mat-standard-button-toggle-selected-state-background-color:#dae2f9;--mat-standard-button-toggle-selected-state-text-color:#131c2b;--mat-standard-button-toggle-disabled-state-text-color:rgba(26, 27, 31, .38);--mat-standard-button-toggle-disabled-selected-state-text-color:rgba(26, 27, 31, .38);--mat-standard-button-toggle-disabled-selected-state-background-color:rgba(26, 27, 31, .12);--mat-standard-button-toggle-divider-color:#74777f;--mat-standard-button-toggle-label-text-font:Roboto, sans-serif;--mat-standard-button-toggle-label-text-line-height:1.25rem;--mat-standard-button-toggle-label-text-size:.875rem;--mat-standard-button-toggle-label-text-tracking:.006rem;--mat-standard-button-toggle-label-text-weight:500;--mat-standard-button-toggle-height:40px;--mat-standard-button-toggle-shape:9999px;--mat-standard-button-toggle-background-color:transparent;--mat-standard-button-toggle-disabled-state-background-color:transparent;--mat-datepicker-calendar-date-selected-state-text-color:#ffffff;--mat-datepicker-calendar-date-selected-state-background-color:#005cbb;--mat-datepicker-calendar-date-selected-disabled-state-background-color:rgba(26, 27, 31, .38);--mat-datepicker-calendar-date-today-selected-state-outline-color:#005cbb;--mat-datepicker-calendar-date-focus-state-background-color:rgba(26, 27, 31, .12);--mat-datepicker-calendar-date-hover-state-background-color:rgba(26, 27, 31, .08);--mat-datepicker-toggle-active-state-icon-color:#44474e;--mat-datepicker-calendar-date-in-range-state-background-color:#d7e3ff;--mat-datepicker-calendar-date-in-comparison-range-state-background-color:#e0e0ff;--mat-datepicker-calendar-date-in-overlap-range-state-background-color:#dae2f9;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color:#565e71;--mat-datepicker-toggle-icon-color:#44474e;--mat-datepicker-calendar-body-label-text-color:#1a1b1f;--mat-datepicker-calendar-period-button-text-color:#44474e;--mat-datepicker-calendar-period-button-icon-color:#44474e;--mat-datepicker-calendar-navigation-button-icon-color:#44474e;--mat-datepicker-calendar-header-text-color:#44474e;--mat-datepicker-calendar-date-today-outline-color:#005cbb;--mat-datepicker-calendar-date-today-disabled-state-outline-color:rgba(26, 27, 31, .38);--mat-datepicker-calendar-date-text-color:#1a1b1f;--mat-datepicker-calendar-date-disabled-state-text-color:rgba(26, 27, 31, .38);--mat-datepicker-calendar-date-preview-state-outline-color:#005cbb;--mat-datepicker-range-input-separator-color:#1a1b1f;--mat-datepicker-range-input-disabled-state-separator-color:rgba(26, 27, 31, .38);--mat-datepicker-range-input-disabled-state-text-color:rgba(26, 27, 31, .38);--mat-datepicker-calendar-container-background-color:#e9e7eb;--mat-datepicker-calendar-container-text-color:#1a1b1f;--mat-datepicker-calendar-text-font:Roboto, sans-serif;--mat-datepicker-calendar-text-size:.875rem;--mat-datepicker-calendar-body-label-text-size:.875rem;--mat-datepicker-calendar-body-label-text-weight:500;--mat-datepicker-calendar-period-button-text-size:.875rem;--mat-datepicker-calendar-period-button-text-weight:500;--mat-datepicker-calendar-header-text-size:.875rem;--mat-datepicker-calendar-header-text-weight:500;--mat-datepicker-calendar-container-shape:16px;--mat-datepicker-calendar-container-touch-shape:28px;--mat-datepicker-calendar-container-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-datepicker-calendar-container-touch-elevation-shadow:0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-datepicker-calendar-header-divider-color:transparent;--mat-datepicker-calendar-date-outline-color:transparent;--mat-divider-color:#74777f;--mat-divider-width:1px;--mat-expansion-container-background-color:#faf9fd;--mat-expansion-container-text-color:#1a1b1f;--mat-expansion-actions-divider-color:#74777f;--mat-expansion-header-hover-state-layer-color:rgba(26, 27, 31, .08);--mat-expansion-header-focus-state-layer-color:rgba(26, 27, 31, .12);--mat-expansion-header-disabled-state-text-color:rgba(26, 27, 31, .38);--mat-expansion-header-text-color:#1a1b1f;--mat-expansion-header-description-color:#44474e;--mat-expansion-header-indicator-color:#44474e;--mat-expansion-header-text-font:Roboto, sans-serif;--mat-expansion-header-text-size:1rem;--mat-expansion-header-text-weight:500;--mat-expansion-header-text-line-height:1.5rem;--mat-expansion-header-text-tracking:.009rem;--mat-expansion-container-text-font:Roboto, sans-serif;--mat-expansion-container-text-line-height:1.5rem;--mat-expansion-container-text-size:1rem;--mat-expansion-container-text-tracking:.031rem;--mat-expansion-container-text-weight:400;--mat-expansion-header-collapsed-state-height:48px;--mat-expansion-header-expanded-state-height:64px;--mat-expansion-container-shape:12px;--mat-expansion-legacy-header-indicator-display:none;--mat-expansion-header-indicator-display:inline-block;--mat-grid-list-tile-header-primary-text-size:400 1rem / 1.5rem Roboto, sans-serif;--mat-grid-list-tile-header-secondary-text-size:400 .875rem / 1.25rem Roboto, sans-serif;--mat-grid-list-tile-footer-primary-text-size:400 1rem / 1.5rem Roboto, sans-serif;--mat-grid-list-tile-footer-secondary-text-size:400 .875rem / 1.25rem Roboto, sans-serif;--mat-icon-color:inherit;--mat-sidenav-container-background-color:#faf9fd;--mat-sidenav-container-text-color:#44474e;--mat-sidenav-content-background-color:#faf9fd;--mat-sidenav-content-text-color:#1a1b1f;--mat-sidenav-scrim-color:rgba(45, 48, 56, .4);--mat-sidenav-container-shape:16px;--mat-sidenav-container-elevation-shadow:none;--mat-sidenav-container-width:360px;--mat-sidenav-container-divider-color:transparent;--mat-stepper-header-icon-foreground-color:#faf9fd;--mat-stepper-header-selected-state-icon-background-color:#005cbb;--mat-stepper-header-selected-state-icon-foreground-color:#ffffff;--mat-stepper-header-edit-state-icon-background-color:#005cbb;--mat-stepper-header-edit-state-icon-foreground-color:#ffffff;--mat-stepper-container-color:#faf9fd;--mat-stepper-line-color:#74777f;--mat-stepper-header-hover-state-layer-color:rgba(47, 48, 51, .08);--mat-stepper-header-focus-state-layer-color:rgba(47, 48, 51, .12);--mat-stepper-header-label-text-color:#44474e;--mat-stepper-header-optional-label-text-color:#44474e;--mat-stepper-header-selected-state-label-text-color:#44474e;--mat-stepper-header-error-state-label-text-color:#ba1a1a;--mat-stepper-header-icon-background-color:#44474e;--mat-stepper-header-error-state-icon-foreground-color:#ba1a1a;--mat-stepper-container-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-font:Roboto, sans-serif;--mat-stepper-header-label-text-size:.875rem;--mat-stepper-header-label-text-weight:500;--mat-stepper-header-error-state-label-text-size:.875rem;--mat-stepper-header-selected-state-label-text-size:.875rem;--mat-stepper-header-selected-state-label-text-weight:500;--mat-stepper-header-height:72px;--mat-stepper-header-focus-state-layer-shape:12px;--mat-stepper-header-hover-state-layer-shape:12px;--mat-stepper-header-error-state-icon-background-color:transparent;--mat-sort-arrow-color:#1a1b1f;--mat-toolbar-container-background-color:#faf9fd;--mat-toolbar-container-text-color:#1a1b1f;--mat-toolbar-title-text-font:Roboto, sans-serif;--mat-toolbar-title-text-line-height:1.75rem;--mat-toolbar-title-text-size:1.375rem;--mat-toolbar-title-text-tracking:0;--mat-toolbar-title-text-weight:400;--mat-toolbar-standard-height:64px;--mat-toolbar-mobile-height:56px;--mat-tree-container-background-color:#faf9fd;--mat-tree-node-text-color:#1a1b1f;--mat-tree-node-text-font:Roboto, sans-serif;--mat-tree-node-text-size:1rem;--mat-tree-node-text-weight:400;--mat-tree-node-min-height:48px}.mat-typography{font:400 1rem/1.5rem Roboto,sans-serif;letter-spacing:.031rem}:root{--primary-color:#6366f1;--primary-dark:#4f46e5;--primary-light:#8b5cf6;--primary-lighter:#a5b4fc;--primary-lightest:#e0e7ff;--secondary-color:#10b981;--secondary-dark:#059669;--secondary-light:#34d399;--secondary-lighter:#6ee7b7;--secondary-lightest:#d1fae5;--accent-color:#06b6d4;--accent-dark:#0891b2;--accent-light:#22d3ee;--accent-lighter:#67e8f9;--accent-lightest:#cffafe;--success-color:#10b981;--warning-color:#f97316;--error-color:#ef4444;--info-color:#06b6d4;--background-color:#f8fafc;--background-secondary:#f1f5f9;--surface-color:#ffffff;--surface-secondary:#f8fafc;--surface-elevated:#ffffff;--text-primary:#0f172a;--text-secondary:#475569;--text-tertiary:#64748b;--text-muted:#94a3b8;--text-inverse:#ffffff;--border-color:#e2e8f0;--border-secondary:#cbd5e1;--border-focus:var(--primary-color);--border-error:var(--error-color);--shadow-xs:0 1px 2px 0 rgba(0, 0, 0, .05);--shadow-sm:0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px 0 rgba(0, 0, 0, .06);--shadow-md:0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);--shadow-lg:0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -2px rgba(0, 0, 0, .05);--shadow-xl:0 20px 25px -5px rgba(0, 0, 0, .1), 0 10px 10px -5px rgba(0, 0, 0, .04);--shadow-2xl:0 25px 50px -12px rgba(0, 0, 0, .25);--shadow-inner:inset 0 2px 4px 0 rgba(0, 0, 0, .06);--shadow-light:var(--shadow-sm);--shadow-medium:var(--shadow-md);--shadow-large:var(--shadow-lg);--hover-overlay:rgba(0, 0, 0, .05);--focus-ring:0 0 0 3px rgba(99, 102, 241, .1);--active-overlay:rgba(0, 0, 0, .1);--gradient-primary:linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);--gradient-secondary:linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);--gradient-background:linear-gradient(135deg, var(--background-color) 0%, var(--background-secondary) 100%);--gradient-surface:linear-gradient(135deg, var(--surface-color) 0%, var(--surface-secondary) 100%)}*{margin:0;padding:0;box-sizing:border-box}body{font-family:Inter,Segoe UI,Tahoma,Geneva,Verdana,sans-serif;background:var(--gradient-background);color:var(--text-primary);line-height:1.6;min-height:100vh;font-size:16px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}html,body{height:100%}body{margin:0;font-family:Roboto,Helvetica Neue,sans-serif}</style><link rel="stylesheet" href="styles-Q6SZF26P.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles-Q6SZF26P.css"></noscript></head>
<body class="mat-typography">
  <app-root></app-root>
<link rel="modulepreload" href="chunk-3Y4XJPOZ.js"><link rel="modulepreload" href="chunk-5P3CUSN4.js"><script src="polyfills-FFHMD2TL.js" type="module"></script><script src="main-U5TOGYRC.js" type="module"></script></body>
</html>
