var od=Object.defineProperty,id=Object.defineProperties;var sd=Object.getOwnPropertyDescriptors;var ln=Object.getOwnPropertySymbols;var Rs=Object.prototype.hasOwnProperty,Fs=Object.prototype.propertyIsEnumerable;var Os=(e,t,n)=>t in e?od(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ie=(e,t)=>{for(var n in t||={})Rs.call(t,n)&&Os(e,n,t[n]);if(ln)for(var n of ln(t))Fs.call(t,n)&&Os(e,n,t[n]);return e},we=(e,t)=>id(e,sd(t));var Nv=(e,t)=>{var n={};for(var r in e)Rs.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&ln)for(var r of ln(e))t.indexOf(r)<0&&Fs.call(e,r)&&(n[r]=e[r]);return n};var ad=(e,t,n)=>new Promise((r,o)=>{var i=u=>{try{a(n.next(u))}catch(c){o(c)}},s=u=>{try{a(n.throw(u))}catch(c){o(c)}},a=u=>u.done?r(u.value):Promise.resolve(u.value).then(i,s);a((n=n.apply(e,t)).next())});function D(e){return typeof e=="function"}function pt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var dn=pt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Ue(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var k=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(D(r))try{r()}catch(i){t=i instanceof dn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Ps(i)}catch(s){t=t??[],s instanceof dn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new dn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Ps(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Ue(n,t)}remove(t){let{_finalizers:n}=this;n&&Ue(n,t),t instanceof e&&t._removeParent(this)}};k.EMPTY=(()=>{let e=new k;return e.closed=!0,e})();var Zr=k.EMPTY;function fn(e){return e instanceof k||e&&"closed"in e&&D(e.remove)&&D(e.add)&&D(e.unsubscribe)}function Ps(e){D(e)?e():e.unsubscribe()}var se={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var ht={setTimeout(e,t,...n){let{delegate:r}=ht;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=ht;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function pn(e){ht.setTimeout(()=>{let{onUnhandledError:t}=se;if(t)t(e);else throw e})}function Ee(){}var ks=Yr("C",void 0,void 0);function Ls(e){return Yr("E",void 0,e)}function js(e){return Yr("N",e,void 0)}function Yr(e,t,n){return{kind:e,value:t,error:n}}var ze=null;function gt(e){if(se.useDeprecatedSynchronousErrorHandling){let t=!ze;if(t&&(ze={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=ze;if(ze=null,n)throw r}}else e()}function Vs(e){se.useDeprecatedSynchronousErrorHandling&&ze&&(ze.errorThrown=!0,ze.error=e)}var Ge=class extends k{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,fn(t)&&t.add(this)):this.destination=ld}static create(t,n,r){return new Ce(t,n,r)}next(t){this.isStopped?Kr(js(t),this):this._next(t)}error(t){this.isStopped?Kr(Ls(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Kr(ks,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},ud=Function.prototype.bind;function Qr(e,t){return ud.call(e,t)}var Jr=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){hn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){hn(r)}else hn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){hn(n)}}},Ce=class extends Ge{constructor(t,n,r){super();let o;if(D(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&se.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Qr(t.next,i),error:t.error&&Qr(t.error,i),complete:t.complete&&Qr(t.complete,i)}):o=t}this.destination=new Jr(o)}};function hn(e){se.useDeprecatedSynchronousErrorHandling?Vs(e):pn(e)}function cd(e){throw e}function Kr(e,t){let{onStoppedNotification:n}=se;n&&ht.setTimeout(()=>n(e,t))}var ld={closed:!0,next:Ee,error:cd,complete:Ee};var mt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function V(e){return e}function dd(...e){return Xr(e)}function Xr(e){return e.length===0?V:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var _=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=pd(n)?n:new Ce(n,r,o);return gt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Bs(r),new r((o,i)=>{let s=new Ce({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[mt](){return this}pipe(...n){return Xr(n)(this)}toPromise(n){return n=Bs(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Bs(e){var t;return(t=e??se.Promise)!==null&&t!==void 0?t:Promise}function fd(e){return e&&D(e.next)&&D(e.error)&&D(e.complete)}function pd(e){return e&&e instanceof Ge||fd(e)&&fn(e)}var $s=pt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ee=(()=>{class e extends _{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new gn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new $s}next(n){gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){gt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Zr:(this.currentObservers=null,i.push(n),new k(()=>{this.currentObservers=null,Ue(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new _;return n.source=this,n}}return e.create=(t,n)=>new gn(t,n),e})(),gn=class extends ee{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Zr}};function eo(e){return D(e?.lift)}function m(e){return t=>{if(eo(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function g(e,t,n,r,o){return new Bt(e,t,n,r,o)}var Bt=class extends Ge{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Us(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}function Hs(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function We(e){return this instanceof We?(this.v=e,this):new We(e)}function zs(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(y){return new Promise(function(N,C){i.push([f,y,N,C])>1||u(f,y)})},h&&(o[f]=h(o[f])))}function u(f,h){try{c(r[f](h))}catch(y){p(i[0][3],y)}}function c(f){f.value instanceof We?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){u("next",f)}function d(f){u("throw",f)}function p(f,h){f(h),i.shift(),i.length&&u(i[0][0],i[0][1])}}function Gs(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Hs=="function"?Hs(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var mn=e=>e&&typeof e.length=="number"&&typeof e!="function";function yn(e){return D(e?.then)}function vn(e){return D(e[mt])}function Dn(e){return Symbol.asyncIterator&&D(e?.[Symbol.asyncIterator])}function In(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function hd(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var wn=hd();function En(e){return D(e?.[wn])}function Cn(e){return zs(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield We(n.read());if(o)return yield We(void 0);yield yield We(r)}}finally{n.releaseLock()}})}function bn(e){return D(e?.getReader)}function S(e){if(e instanceof _)return e;if(e!=null){if(vn(e))return gd(e);if(mn(e))return md(e);if(yn(e))return yd(e);if(Dn(e))return Ws(e);if(En(e))return vd(e);if(bn(e))return Dd(e)}throw In(e)}function gd(e){return new _(t=>{let n=e[mt]();if(D(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function md(e){return new _(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function yd(e){return new _(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,pn)})}function vd(e){return new _(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Ws(e){return new _(t=>{Id(e,t).catch(n=>t.error(n))})}function Dd(e){return Ws(Cn(e))}function Id(e,t){var n,r,o,i;return Us(this,void 0,void 0,function*(){try{for(n=Gs(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function qs(e){return m((t,n)=>{S(e).subscribe(g(n,()=>n.complete(),Ee)),!n.closed&&t.subscribe(n)})}function to(){return m((e,t)=>{let n=null;e._refCount++;let r=g(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var no=class extends _{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,eo(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new k;let n=this.getSubject();t.add(this.source.subscribe(g(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=k.EMPTY)}return t}refCount(){return to()(this)}};var $t=class extends ee{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var ro={now(){return(ro.delegate||Date).now()},delegate:void 0};var _n=class extends k{constructor(t,n){super()}schedule(t,n=0){return this}};var Ht={setInterval(e,t,...n){let{delegate:r}=Ht;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Ht;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var yt=class extends _n{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Ht.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Ht.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Ue(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var vt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};vt.now=ro.now;var Dt=class extends vt{constructor(t,n=vt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Zs=new Dt(yt);var Mn=class extends yt{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}schedule(t,n=0){return n>0?super.schedule(t,n):(this.delay=n,this.state=t,this.scheduler.flush(this),this)}execute(t,n){return n>0||this.closed?super.execute(t,n):this._execute(t,n)}requestAsyncId(t,n,r=0){return r!=null&&r>0||r==null&&this.delay>0?super.requestAsyncId(t,n,r):(t.flush(this),0)}};var xn=class extends Dt{};var wd=new xn(Mn);var be=new _(e=>e.complete());function Ys(e){return e&&D(e.schedule)}function oo(e){return e[e.length-1]}function It(e){return D(oo(e))?e.pop():void 0}function pe(e){return Ys(oo(e))?e.pop():void 0}function Qs(e,t){return typeof oo(e)=="number"?e.pop():t}function z(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ut(e,t=0){return m((n,r)=>{n.subscribe(g(r,o=>z(r,e,()=>r.next(o),t),()=>z(r,e,()=>r.complete(),t),o=>z(r,e,()=>r.error(o),t)))})}function Sn(e,t=0){return m((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ks(e,t){return S(e).pipe(Sn(t),Ut(t))}function Js(e,t){return S(e).pipe(Sn(t),Ut(t))}function Xs(e,t){return new _(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ea(e,t){return new _(n=>{let r;return z(n,t,()=>{r=e[wn](),z(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>D(r?.return)&&r.return()})}function Tn(e,t){if(!e)throw new Error("Iterable cannot be null");return new _(n=>{z(n,t,()=>{let r=e[Symbol.asyncIterator]();z(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function ta(e,t){return Tn(Cn(e),t)}function na(e,t){if(e!=null){if(vn(e))return Ks(e,t);if(mn(e))return Xs(e,t);if(yn(e))return Js(e,t);if(Dn(e))return Tn(e,t);if(En(e))return ea(e,t);if(bn(e))return ta(e,t)}throw In(e)}function he(e,t){return t?na(e,t):S(e)}function io(...e){let t=pe(e);return he(e,t)}function so(e,t){let n=D(e)?e:()=>e,r=o=>o.error(n());return new _(t?o=>t.schedule(r,0,o):r)}var Oe=class e{constructor(t,n,r){this.kind=t,this.value=n,this.error=r,this.hasValue=t==="N"}observe(t){return ao(this,t)}do(t,n,r){let{kind:o,value:i,error:s}=this;return o==="N"?t?.(i):o==="E"?n?.(s):r?.()}accept(t,n,r){var o;return D((o=t)===null||o===void 0?void 0:o.next)?this.observe(t):this.do(t,n,r)}toObservable(){let{kind:t,value:n,error:r}=this,o=t==="N"?io(n):t==="E"?so(()=>r):t==="C"?be:0;if(!o)throw new TypeError(`Unexpected notification kind ${t}`);return o}static createNext(t){return new e("N",t)}static createError(t){return new e("E",void 0,t)}static createComplete(){return e.completeNotification}};Oe.completeNotification=new Oe("C");function ao(e,t){var n,r,o;let{kind:i,value:s,error:a}=e;if(typeof i!="string")throw new TypeError('Invalid notification, missing "kind"');i==="N"?(n=t.next)===null||n===void 0||n.call(t,s):i==="E"?(r=t.error)===null||r===void 0||r.call(t,a):(o=t.complete)===null||o===void 0||o.call(t)}function Ed(e){return!!e&&(e instanceof _||D(e.lift)&&D(e.subscribe))}var qe=pt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function J(e,t){return m((n,r)=>{let o=0;n.subscribe(g(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Cd}=Array;function bd(e,t){return Cd(t)?e(...t):e(t)}function Nn(e){return J(t=>bd(e,t))}var{isArray:_d}=Array,{getPrototypeOf:Md,prototype:xd,keys:Sd}=Object;function An(e){if(e.length===1){let t=e[0];if(_d(t))return{args:t,keys:null};if(Td(t)){let n=Sd(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Td(e){return e&&typeof e=="object"&&Md(e)===xd}function On(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Nd(...e){let t=pe(e),n=It(e),{args:r,keys:o}=An(e);if(r.length===0)return he([],t);let i=new _(Ad(r,t,o?s=>On(o,s):V));return n?i.pipe(Nn(n)):i}function Ad(e,t,n=V){return r=>{ra(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)ra(t,()=>{let c=he(e[u],t),l=!1;c.subscribe(g(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ra(e,t,n){e?z(n,e,t):t()}function oa(e,t,n,r,o,i,s,a){let u=[],c=0,l=0,d=!1,p=()=>{d&&!u.length&&!c&&t.complete()},f=y=>c<r?h(y):u.push(y),h=y=>{i&&t.next(y),c++;let N=!1;S(n(y,l++)).subscribe(g(t,C=>{o?.(C),i?f(C):t.next(C)},()=>{N=!0},void 0,()=>{if(N)try{for(c--;u.length&&c<r;){let C=u.shift();s?z(t,s,()=>h(C)):h(C)}p()}catch(C){t.error(C)}}))};return e.subscribe(g(t,f,()=>{d=!0,p()})),()=>{a?.()}}function Ze(e,t,n=1/0){return D(t)?Ze((r,o)=>J((i,s)=>t(r,i,o,s))(S(e(r,o))),n):(typeof t=="number"&&(n=t),m((r,o)=>oa(r,o,e,n)))}function Rn(e=1/0){return Ze(V,e)}function ia(){return Rn(1)}function Fn(...e){return ia()(he(e,pe(e)))}function Od(e){return new _(t=>{S(e()).subscribe(t)})}function Rd(...e){let t=It(e),{args:n,keys:r}=An(e),o=new _(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;S(n[l]).subscribe(g(i,p=>{d||(d=!0,c--),a[l]=p},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?On(r,a):a),i.complete())}))}});return t?o.pipe(Nn(t)):o}function Fd(...e){let t=pe(e),n=Qs(e,1/0),r=e;return r.length?r.length===1?S(r[0]):Rn(n)(he(r,t)):be}function Ye(e,t){return m((n,r)=>{let o=0;n.subscribe(g(r,i=>e.call(t,i,o++)&&r.next(i)))})}function uo(e){return m((t,n)=>{let r=null,o=!1,i;r=t.subscribe(g(n,void 0,void 0,s=>{i=S(e(s,uo(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function sa(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(g(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function Pd(e,t){return D(t)?Ze(e,t,1):Ze(e,1)}function aa(e,t=Zs){return m((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),r.add(o);return}a()}n.subscribe(g(r,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function zt(e){return m((t,n)=>{let r=!1;t.subscribe(g(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function co(e){return e<=0?()=>be:m((t,n)=>{let r=0;t.subscribe(g(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function kd(){return m((e,t)=>{e.subscribe(g(t,Ee))})}function Ld(e){return J(()=>e)}function jd(){return m((e,t)=>{e.subscribe(g(t,n=>ao(n,t)))})}function ua(e,t=V){return e=e??Vd,m((n,r)=>{let o,i=!0;n.subscribe(g(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function Vd(e,t){return e===t}function Pn(e=Bd){return m((t,n)=>{let r=!1;t.subscribe(g(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Bd(){return new qe}function ca(e,t){return t?n=>n.pipe(ca((r,o)=>S(e(r,o)).pipe(J((i,s)=>t(r,i,o,s))))):m((n,r)=>{let o=0,i=null,s=!1;n.subscribe(g(r,a=>{i||(i=g(r,void 0,()=>{i=null,s&&r.complete()}),S(e(a,o++)).subscribe(i))},()=>{s=!0,!i&&r.complete()}))})}function $d(e){return m((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function la(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ye((o,i)=>e(o,i,r)):V,co(1),n?zt(t):Pn(()=>new qe))}function Hd(e,t,n,r){return m((o,i)=>{let s;!t||typeof t=="function"?s=t:{duration:n,element:s,connector:r}=t;let a=new Map,u=h=>{a.forEach(h),h(i)},c=h=>u(y=>y.error(h)),l=0,d=!1,p=new Bt(i,h=>{try{let y=e(h),N=a.get(y);if(!N){a.set(y,N=r?r():new ee);let C=f(y,N);if(i.next(C),n){let R=g(N,()=>{N.complete(),R?.unsubscribe()},void 0,void 0,()=>a.delete(y));p.add(S(n(C)).subscribe(R))}}N.next(s?s(h):h)}catch(y){c(y)}},()=>u(h=>h.complete()),c,()=>a.clear(),()=>(d=!0,l===0));o.subscribe(p);function f(h,y){let N=new _(C=>{l++;let R=y.subscribe(C);return()=>{R.unsubscribe(),--l===0&&d&&p.unsubscribe()}});return N.key=h,N}})}function lo(e){return e<=0?()=>be:m((t,n)=>{let r=[];t.subscribe(g(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Ud(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ye((o,i)=>e(o,i,r)):V,lo(1),n?zt(t):Pn(()=>new qe))}function zd(){return m((e,t)=>{e.subscribe(g(t,n=>{t.next(Oe.createNext(n))},()=>{t.next(Oe.createComplete()),t.complete()},n=>{t.next(Oe.createError(n)),t.complete()}))})}function Gd(...e){let t=e.length;if(t===0)throw new Error("list of properties cannot be empty.");return J(n=>{let r=n;for(let o=0;o<t;o++){let i=r?.[e[o]];if(typeof i<"u")r=i;else return}return r})}function Wd(e,t){return m(sa(e,t,arguments.length>=2,!0))}function qd(e={}){let{connector:t=()=>new ee,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=u=void 0,l=d=!1},h=()=>{let y=s;f(),y?.unsubscribe()};return m((y,N)=>{c++,!d&&!l&&p();let C=u=u??t();N.add(()=>{c--,c===0&&!d&&!l&&(a=fo(h,o))}),C.subscribe(N),!s&&c>0&&(s=new Ce({next:R=>C.next(R),error:R=>{d=!0,p(),a=fo(f,n,R),C.error(R)},complete:()=>{l=!0,p(),a=fo(f,r),C.complete()}}),S(y).subscribe(s))})(i)}}function fo(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ce({next:()=>{r.unsubscribe(),e()}});return S(t(...n)).subscribe(r)}function Zd(e){return Ye((t,n)=>e<=n)}function Yd(...e){let t=pe(e);return m((n,r)=>{(t?Fn(e,n,t):Fn(e,n)).subscribe(r)})}function Qd(e,t){return m((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(g(r,u=>{o?.unsubscribe();let c=0,l=i++;S(e(u,l)).subscribe(o=g(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Kd(e,t,n){let r=D(e)||t||n?{next:e,error:t,complete:n}:e;return r?m((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(g(i,u=>{var c;(c=r.next)===null||c===void 0||c.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,u),i.error(u)},()=>{var u,c;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):V}function Jd(...e){let t=It(e);return m((n,r)=>{let o=e.length,i=new Array(o),s=e.map(()=>!1),a=!1;for(let u=0;u<o;u++)S(e[u]).subscribe(g(r,c=>{i[u]=c,!a&&!s[u]&&(s[u]=!0,(a=s.every(V))&&(s=null))},Ee));n.subscribe(g(r,u=>{if(a){let c=[u,...i];r.next(t?t(...c):c)}}))})}function da(e,t){return Object.is(e,t)}var j=null,kn=!1,Ln=1,_e=Symbol("SIGNAL");function M(e){let t=j;return j=e,t}function fa(){return j}var Wt={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function go(e){if(kn)throw new Error("");if(j===null)return;j.consumerOnSignalRead(e);let t=j.nextProducerIndex++;if($n(j),t<j.producerNode.length&&j.producerNode[t]!==e&&Gt(j)){let n=j.producerNode[t];Bn(n,j.producerIndexOfThis[t])}j.producerNode[t]!==e&&(j.producerNode[t]=e,j.producerIndexOfThis[t]=Gt(j)?ma(e,j,t):0),j.producerLastReadVersion[t]=e.version}function Xd(){Ln++}function pa(e){if(!(Gt(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ln)){if(!e.producerMustRecompute(e)&&!yo(e)){e.dirty=!1,e.lastCleanEpoch=Ln;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Ln}}function ha(e){if(e.liveConsumerNode===void 0)return;let t=kn;kn=!0;try{for(let n of e.liveConsumerNode)n.dirty||ef(n)}finally{kn=t}}function ga(){return j?.consumerAllowSignalWrites!==!1}function ef(e){e.dirty=!0,ha(e),e.consumerMarkedDirty?.(e)}function Vn(e){return e&&(e.nextProducerIndex=0),M(e)}function mo(e,t){if(M(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Gt(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Bn(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function yo(e){$n(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(pa(n),r!==n.version))return!0}return!1}function vo(e){if($n(e),Gt(e))for(let t=0;t<e.producerNode.length;t++)Bn(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ma(e,t,n){if(ya(e),e.liveConsumerNode.length===0&&va(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=ma(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Bn(e,t){if(ya(e),e.liveConsumerNode.length===1&&va(e))for(let r=0;r<e.producerNode.length;r++)Bn(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];$n(o),o.producerIndexOfThis[r]=t}}function Gt(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function $n(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ya(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function va(e){return e.producerNode!==void 0}function Da(e){let t=Object.create(tf);t.computation=e;let n=()=>{if(pa(t),go(t),t.value===jn)throw t.error;return t.value};return n[_e]=t,n}var po=Symbol("UNSET"),ho=Symbol("COMPUTING"),jn=Symbol("ERRORED"),tf=we(Ie({},Wt),{value:po,dirty:!0,error:null,equal:da,producerMustRecompute(e){return e.value===po||e.value===ho},producerRecomputeValue(e){if(e.value===ho)throw new Error("Detected cycle in computations.");let t=e.value;e.value=ho;let n=Vn(e),r;try{r=e.computation()}catch(o){r=jn,e.error=o}finally{mo(e,n)}if(t!==po&&t!==jn&&r!==jn&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function nf(){throw new Error}var Ia=nf;function wa(){Ia()}function Ea(e){Ia=e}var rf=null;function Ca(e){let t=Object.create(_a);t.value=e;let n=()=>(go(t),t.value);return n[_e]=t,n}function Do(e,t){ga()||wa(),e.equal(e.value,t)||(e.value=t,of(e))}function ba(e,t){ga()||wa(),Do(e,t(e.value))}var _a=we(Ie({},Wt),{equal:da,value:void 0});function of(e){e.version++,Xd(),ha(e),rf?.()}var du="https://g.co/ng/security#xss",x=class extends Error{constructor(t,n){super(fu(t,n)),this.code=t}};function fu(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function sn(e){return{toString:e}.toString()}var Hn="__parameters__";function sf(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Bi(e,t,n){return sn(()=>{let r=sf(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(Hn)?u[Hn]:Object.defineProperty(u,Hn,{value:[]})[Hn];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ma=globalThis;function O(e){for(let t in e)if(e[t]===O)return t;throw Error("Could not find renamed property on target object.")}function af(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function U(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(U).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Ro(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var uf=O({__forward_ref__:O});function pu(e){return e.__forward_ref__=pu,e.toString=function(){return U(this())},e}function H(e){return hu(e)?e():e}function hu(e){return typeof e=="function"&&e.hasOwnProperty(uf)&&e.__forward_ref__===pu}function P(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function gu(e){return{providers:e.providers||[],imports:e.imports||[]}}function _r(e){return xa(e,mu)||xa(e,yu)}function lM(e){return _r(e)!==null}function xa(e,t){return e.hasOwnProperty(t)?e[t]:null}function cf(e){let t=e&&(e[mu]||e[yu]);return t||null}function Sa(e){return e&&(e.hasOwnProperty(Ta)||e.hasOwnProperty(lf))?e[Ta]:null}var mu=O({\u0275prov:O}),Ta=O({\u0275inj:O}),yu=O({ngInjectableDef:O}),lf=O({ngInjectorDef:O}),A=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=P({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function vu(e){return e&&!!e.\u0275providers}var df=O({\u0275cmp:O}),ff=O({\u0275dir:O}),pf=O({\u0275pipe:O}),hf=O({\u0275mod:O}),er=O({\u0275fac:O}),Yt=O({__NG_ELEMENT_ID__:O}),Na=O({__NG_ENV_ID__:O});function Mt(e){return typeof e=="string"?e:e==null?"":String(e)}function gf(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Mt(e)}function mf(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new x(-200,e)}function $i(e,t){throw new x(-201,!1)}var E=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(E||{}),Fo;function Du(){return Fo}function G(e){let t=Fo;return Fo=e,t}function Iu(e,t,n){let r=_r(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&E.Optional)return null;if(t!==void 0)return t;$i(e,"Injector")}var yf={},Qt=yf,Po="__NG_DI_FLAG__",tr="ngTempTokenPath",vf="ngTokenPath",Df=/\n/gm,If="\u0275",Aa="__source",bt;function wf(){return bt}function Re(e){let t=bt;return bt=e,t}function Ef(e,t=E.Default){if(bt===void 0)throw new x(-203,!1);return bt===null?Iu(e,void 0,t):bt.get(e,t&E.Optional?null:void 0,t)}function Z(e,t=E.Default){return(Du()||Ef)(H(e),t)}function T(e,t=E.Default){return Z(e,Mr(t))}function Mr(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function ko(e){let t=[];for(let n=0;n<e.length;n++){let r=H(e[n]);if(Array.isArray(r)){if(r.length===0)throw new x(900,!1);let o,i=E.Default;for(let s=0;s<r.length;s++){let a=r[s],u=Cf(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(Z(o,i))}else t.push(Z(r))}return t}function Hi(e,t){return e[Po]=t,e.prototype[Po]=t,e}function Cf(e){return e[Po]}function bf(e,t,n,r){let o=e[tr];throw t[Aa]&&o.unshift(t[Aa]),e.message=_f(`
`+e.message,o,n,r),e[vf]=o,e[tr]=null,e}function _f(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==If?e.slice(2):e;let o=U(t);if(Array.isArray(t))o=t.map(U).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):U(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Df,`
  `)}`}var dM=Hi(Bi("Inject",e=>({token:e})),-1),Mf=Hi(Bi("Optional"),8);var xf=Hi(Bi("SkipSelf"),4);function Ke(e,t){let n=e.hasOwnProperty(er);return n?e[er]:null}function Sf(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Tf(e){return e.flat(Number.POSITIVE_INFINITY)}function Ui(e,t){e.forEach(n=>Array.isArray(n)?Ui(n,t):t(n))}function wu(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function nr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Nf(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function Af(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function zi(e,t,n){let r=an(e,t);return r>=0?e[r|1]=n:(r=~r,Af(e,r,t,n)),r}function Io(e,t){let n=an(e,t);if(n>=0)return e[n|1]}function an(e,t){return Of(e,t,1)}function Of(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var xt={},W=[],rr=new A(""),Eu=new A("",-1),Cu=new A(""),or=class{get(t,n=Qt){if(n===Qt){let r=new Error(`NullInjectorError: No provider for ${U(t)}!`);throw r.name="NullInjectorError",r}return n}},bu=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(bu||{}),Kt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Kt||{}),ke=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ke||{});function Rf(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function Lo(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Ff(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function _u(e){return e===3||e===4||e===6}function Ff(e){return e.charCodeAt(0)===64}function Jt(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Oa(e,n,o,null,t[++r]):Oa(e,n,o,null,null))}}return e}function Oa(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var Mu="ng-template";function Pf(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&Rf(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Gi(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Gi(e){return e.type===4&&e.value!==Mu}function kf(e,t,n){let r=e.type===4&&!n?Mu:e.value;return t===r}function Lf(e,t,n){let r=4,o=e.attrs,i=o!==null?Bf(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!ae(r)&&!ae(u))return!1;if(s&&ae(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!kf(e,u,n)||u===""&&t.length===1){if(ae(r))return!1;s=!0}}else if(r&8){if(o===null||!Pf(e,o,u,n)){if(ae(r))return!1;s=!0}}else{let c=t[++a],l=jf(u,o,Gi(e),n);if(l===-1){if(ae(r))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&c!==d){if(ae(r))return!1;s=!0}}}}return ae(r)||s}function ae(e){return(e&1)===0}function jf(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return $f(t,e)}function xu(e,t,n=!1){for(let r=0;r<t.length;r++)if(Lf(e,t[r],n))return!0;return!1}function Vf(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if(!(n&1))return t[n+1]}return null}function Bf(e){for(let t=0;t<e.length;t++){let n=e[t];if(_u(n))return t}return e.length}function $f(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Hf(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Ra(e,t){return e?":not("+t.trim()+")":t}function Uf(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ae(s)&&(t+=Ra(i,o),o=""),r=s,i=i||!ae(r);n++}return o!==""&&(t+=Ra(i,o)),t}function zf(e){return e.map(Uf).join(",")}function Gf(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ae(o))break;o=i}r++}return{attrs:t,classes:n}}function fM(e){return sn(()=>{let t=Ru(e),n=we(Ie({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===bu.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Kt.Emulated,styles:e.styles||W,_:null,schemas:e.schemas||null,tView:null,id:""});Fu(n);let r=e.dependencies;return n.directiveDefs=Pa(r,!1),n.pipeDefs=Pa(r,!0),n.id=Yf(n),n})}function Wf(e){return Je(e)||Nu(e)}function qf(e){return e!==null}function Su(e){return sn(()=>({type:e.type,bootstrap:e.bootstrap||W,declarations:e.declarations||W,imports:e.imports||W,exports:e.exports||W,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Fa(e,t){if(e==null)return xt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=ke.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==ke.None?[r,a]:r,t[i]=s):n[i]=r}return n}function xr(e){return sn(()=>{let t=Ru(e);return Fu(t),t})}function Tu(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Je(e){return e[df]||null}function Nu(e){return e[ff]||null}function Au(e){return e[pf]||null}function Zf(e){let t=Je(e)||Nu(e)||Au(e);return t!==null?t.standalone:!1}function Ou(e,t){let n=e[hf]||null;if(!n&&t===!0)throw new Error(`Type ${U(e)} does not have '\u0275mod' property.`);return n}function Ru(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||xt,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||W,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Fa(e.inputs,t),outputs:Fa(e.outputs),debugInfo:null}}function Fu(e){e.features?.forEach(t=>t(e))}function Pa(e,t){if(!e)return null;let n=t?Au:Wf;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(qf)}function Yf(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function Qf(e){return{\u0275providers:e}}function Kf(...e){return{\u0275providers:Pu(!0,e),\u0275fromNgModule:!0}}function Pu(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Ui(t,s=>{let a=s;jo(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&ku(o,i),n}function ku(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Wi(o,i=>{t(i,r)})}}function jo(e,t,n,r){if(e=H(e),!e)return!1;let o=null,i=Sa(e),s=!i&&Je(e);if(!i&&!s){let u=e.ngModule;if(i=Sa(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)jo(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{Ui(i.imports,l=>{jo(l,t,n,r)&&(c||=[],c.push(l))})}finally{}c!==void 0&&ku(c,t)}if(!a){let c=Ke(o)||(()=>new o);t({provide:o,useFactory:c,deps:W},o),t({provide:Cu,useValue:o,multi:!0},o),t({provide:rr,useValue:()=>Z(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;Wi(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Wi(e,t){for(let n of e)vu(n)&&(n=n.\u0275providers),Array.isArray(n)?Wi(n,t):t(n)}var Jf=O({provide:String,useValue:O});function Lu(e){return e!==null&&typeof e=="object"&&Jf in e}function Xf(e){return!!(e&&e.useExisting)}function ep(e){return!!(e&&e.useFactory)}function St(e){return typeof e=="function"}function tp(e){return!!e.useClass}var ju=new A(""),Zn={},np={},wo;function qi(){return wo===void 0&&(wo=new or),wo}var Le=class{},Xt=class extends Le{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Bo(t,s=>this.processProvider(s)),this.records.set(Eu,wt(void 0,this)),o.has("environment")&&this.records.set(Le,wt(void 0,this));let i=this.records.get(ju);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Cu,W,E.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=M(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),M(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=Re(this),r=G(void 0),o;try{return t()}finally{Re(n),G(r)}}get(t,n=Qt,r=E.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Na))return t[Na](this);r=Mr(r);let o,i=Re(this),s=G(void 0);try{if(!(r&E.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=ap(t)&&_r(t);c&&this.injectableDefInScope(c)?u=wt(Vo(t),Zn):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=r&E.Self?qi():this.parent;return n=r&E.Optional&&n===Qt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[tr]=a[tr]||[]).unshift(U(t)),i)throw a;return bf(a,t,"R3InjectorError",this.source)}else throw a}finally{G(s),Re(i)}}resolveInjectorInitializers(){let t=M(null),n=Re(this),r=G(void 0),o;try{let i=this.get(rr,W,E.Self);for(let s of i)s()}finally{Re(n),G(r),M(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(U(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new x(205,!1)}processProvider(t){t=H(t);let n=St(t)?t:H(t&&t.provide),r=op(t);if(!St(t)&&t.multi===!0){let o=this.records.get(n);o||(o=wt(void 0,Zn,!0),o.factory=()=>ko(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=M(null);try{return n.value===Zn&&(n.value=np,n.value=n.factory()),typeof n.value=="object"&&n.value&&sp(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{M(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=H(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Vo(e){let t=_r(e),n=t!==null?t.factory:Ke(e);if(n!==null)return n;if(e instanceof A)throw new x(204,!1);if(e instanceof Function)return rp(e);throw new x(204,!1)}function rp(e){if(e.length>0)throw new x(204,!1);let n=cf(e);return n!==null?()=>n.factory(e):()=>new e}function op(e){if(Lu(e))return wt(void 0,e.useValue);{let t=Vu(e);return wt(t,Zn)}}function Vu(e,t,n){let r;if(St(e)){let o=H(e);return Ke(o)||Vo(o)}else if(Lu(e))r=()=>H(e.useValue);else if(ep(e))r=()=>e.useFactory(...ko(e.deps||[]));else if(Xf(e))r=()=>Z(H(e.useExisting));else{let o=H(e&&(e.useClass||e.provide));if(ip(e))r=()=>new o(...ko(e.deps));else return Ke(o)||Vo(o)}return r}function wt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function ip(e){return!!e.deps}function sp(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function ap(e){return typeof e=="function"||typeof e=="object"&&e instanceof A}function Bo(e,t){for(let n of e)Array.isArray(n)?Bo(n,t):n&&vu(n)?Bo(n.\u0275providers,t):t(n)}function pM(e,t){e instanceof Xt&&e.assertNotDestroyed();let n,r=Re(e),o=G(void 0);try{return t()}finally{Re(r),G(o)}}function Bu(){return Du()!==void 0||wf()!=null}function hM(e){if(!Bu())throw new x(-203,!1)}function up(e){return typeof e=="function"}var Te=0,w=1,v=2,B=3,le=4,Q=5,en=6,ir=7,de=8,Tt=9,ye=10,F=11,tn=12,ka=13,kt=14,ne=15,Xe=16,Et=17,Me=18,Sr=19,$u=20,Fe=21,Eo=22,te=23,Y=25,Hu=1;var et=7,sr=8,Nt=9,X=10,ar=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(ar||{});function Pe(e){return Array.isArray(e)&&typeof e[Hu]=="object"}function Ne(e){return Array.isArray(e)&&e[Hu]===!0}function Zi(e){return(e.flags&4)!==0}function Tr(e){return e.componentOffset>-1}function Nr(e){return(e.flags&1)===1}function je(e){return!!e.template}function $o(e){return(e[v]&512)!==0}var Ho=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Uu(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function Yi(){return zu}function zu(e){return e.type.prototype.ngOnChanges&&(e.setInput=lp),cp}Yi.ngInherit=!0;function cp(){let e=Wu(this),t=e?.current;if(t){let n=e.previous;if(n===xt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function lp(e,t,n,r,o){let i=this.declaredInputs[r],s=Wu(e)||dp(e,{previous:xt,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new Ho(c&&c.currentValue,n,u===xt),Uu(e,t,o,n)}var Gu="__ngSimpleChanges__";function Wu(e){return e[Gu]||null}function dp(e,t){return e[Gu]=t}var La=null;var ge=function(e,t,n){La?.(e,t,n)},qu="svg",fp="math";function ve(e){for(;Array.isArray(e);)e=e[Te];return e}function Zu(e,t){return ve(t[e])}function re(e,t){return ve(t[e.index])}function Yu(e,t){return e.data[t]}function Qu(e,t){return e[t]}function $e(e,t){let n=t[e];return Pe(n)?n:n[Te]}function pp(e){return(e[v]&4)===4}function Qi(e){return(e[v]&128)===128}function hp(e){return Ne(e[B])}function At(e,t){return t==null?null:e[t]}function Ku(e){e[Et]=0}function Ju(e){e[v]&1024||(e[v]|=1024,Qi(e)&&Or(e))}function gp(e,t){for(;e>0;)t=t[kt],e--;return t}function Ar(e){return!!(e[v]&9216||e[te]?.dirty)}function Uo(e){e[ye].changeDetectionScheduler?.notify(8),e[v]&64&&(e[v]|=1024),Ar(e)&&Or(e)}function Or(e){e[ye].changeDetectionScheduler?.notify(0);let t=tt(e);for(;t!==null&&!(t[v]&8192||(t[v]|=8192,!Qi(t)));)t=tt(t)}function Xu(e,t){if((e[v]&256)===256)throw new x(911,!1);e[Fe]===null&&(e[Fe]=[]),e[Fe].push(t)}function mp(e,t){if(e[Fe]===null)return;let n=e[Fe].indexOf(t);n!==-1&&e[Fe].splice(n,1)}function tt(e){let t=e[B];return Ne(t)?t[B]:t}var I={lFrame:uc(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var ec=!1;function yp(){return I.lFrame.elementDepthCount}function vp(){I.lFrame.elementDepthCount++}function Dp(){I.lFrame.elementDepthCount--}function tc(){return I.bindingsEnabled}function nc(){return I.skipHydrationRootTNode!==null}function Ip(e){return I.skipHydrationRootTNode===e}function wp(){I.skipHydrationRootTNode=null}function b(){return I.lFrame.lView}function L(){return I.lFrame.tView}function gM(e){return I.lFrame.contextLView=e,e[de]}function mM(e){return I.lFrame.contextLView=null,e}function $(){let e=rc();for(;e!==null&&e.type===64;)e=e.parent;return e}function rc(){return I.lFrame.currentTNode}function Ep(){let e=I.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function ut(e,t){let n=I.lFrame;n.currentTNode=e,n.isParent=t}function Ki(){return I.lFrame.isParent}function Ji(){I.lFrame.isParent=!1}function Cp(){return I.lFrame.contextLView}function oc(){return ec}function ja(e){ec=e}function bp(){let e=I.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function _p(){return I.lFrame.bindingIndex}function Mp(e){return I.lFrame.bindingIndex=e}function Rr(){return I.lFrame.bindingIndex++}function Xi(e){let t=I.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function xp(){return I.lFrame.inI18n}function Sp(e,t){let n=I.lFrame;n.bindingIndex=n.bindingRootIndex=e,zo(t)}function Tp(){return I.lFrame.currentDirectiveIndex}function zo(e){I.lFrame.currentDirectiveIndex=e}function Np(e){let t=I.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function ic(){return I.lFrame.currentQueryIndex}function es(e){I.lFrame.currentQueryIndex=e}function Ap(e){let t=e[w];return t.type===2?t.declTNode:t.type===1?e[Q]:null}function sc(e,t,n){if(n&E.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&E.Host);)if(o=Ap(i),o===null||(i=i[kt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=I.lFrame=ac();return r.currentTNode=t,r.lView=e,!0}function ts(e){let t=ac(),n=e[w];I.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function ac(){let e=I.lFrame,t=e===null?null:e.child;return t===null?uc(e):t}function uc(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function cc(){let e=I.lFrame;return I.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var lc=cc;function ns(){let e=cc();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Op(e){return(I.lFrame.contextLView=gp(e,I.lFrame.contextLView))[de]}function He(){return I.lFrame.selectedIndex}function nt(e){I.lFrame.selectedIndex=e}function rs(){let e=I.lFrame;return Yu(e.tView,e.selectedIndex)}function yM(){I.lFrame.currentNamespace=qu}function vM(){Rp()}function Rp(){I.lFrame.currentNamespace=null}function Fp(){return I.lFrame.currentNamespace}var dc=!0;function Fr(){return dc}function Pr(e){dc=e}function Pp(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=zu(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function kr(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:c,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),u&&(e.viewHooks??=[]).push(-n,u),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Yn(e,t,n){fc(e,t,3,n)}function Qn(e,t,n,r){(e[v]&3)===n&&fc(e,t,n,r)}function Co(e,t){let n=e[v];(n&3)===t&&(n&=16383,n+=1,e[v]=n)}function fc(e,t,n,r){let o=r!==void 0?e[Et]&65535:0,i=r??-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],r!=null&&a>=r)break}else t[u]<0&&(e[Et]+=65536),(a<i||i==-1)&&(kp(e,n,t,u),e[Et]=(e[Et]&**********)+u+2),u++}function Va(e,t){ge(4,e,t);let n=M(null);try{t.call(e)}finally{M(n),ge(5,e,t)}}function kp(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[v]>>14<e[Et]>>16&&(e[v]&3)===t&&(e[v]+=16384,Va(a,i)):Va(a,i)}var _t=-1,rt=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function Lp(e){return e instanceof rt}function jp(e){return(e.flags&8)!==0}function Vp(e){return(e.flags&16)!==0}var bo={},Go=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Mr(r);let o=this.injector.get(t,bo,r);return o!==bo||n===bo?o:this.parentInjector.get(t,n,r)}};function pc(e){return e!==_t}function ur(e){return e&32767}function Bp(e){return e>>16}function cr(e,t){let n=Bp(e),r=t;for(;n>0;)r=r[kt],n--;return r}var Wo=!0;function lr(e){let t=Wo;return Wo=e,t}var $p=256,hc=$p-1,gc=5,Hp=0,me={};function Up(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Yt)&&(r=n[Yt]),r==null&&(r=n[Yt]=Hp++);let o=r&hc,i=1<<o;t.data[e+(o>>gc)]|=i}function dr(e,t){let n=mc(e,t);if(n!==-1)return n;let r=t[w];r.firstCreatePass&&(e.injectorIndex=t.length,_o(r.data,e),_o(t,null),_o(r.blueprint,null));let o=os(e,t),i=e.injectorIndex;if(pc(o)){let s=ur(o),a=cr(o,t),u=a[w].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function _o(e,t){e.push(0,0,0,0,0,0,0,0,t)}function mc(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function os(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=wc(o),r===null)return _t;if(n++,o=o[kt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return _t}function qo(e,t,n){Up(e,t,n)}function zp(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(_u(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function yc(e,t,n){if(n&E.Optional||e!==void 0)return e;$i(t,"NodeInjector")}function vc(e,t,n,r){if(n&E.Optional&&r===void 0&&(r=null),!(n&(E.Self|E.Host))){let o=e[Tt],i=G(void 0);try{return o?o.get(t,r,n&E.Optional):Iu(t,r,n&E.Optional)}finally{G(i)}}return yc(r,t,n)}function Dc(e,t,n,r=E.Default,o){if(e!==null){if(t[v]&2048&&!(r&E.Self)){let s=Zp(e,t,n,r,me);if(s!==me)return s}let i=Ic(e,t,n,r,me);if(i!==me)return i}return vc(t,n,r,o)}function Ic(e,t,n,r,o){let i=Wp(n);if(typeof i=="function"){if(!sc(t,e,r))return r&E.Host?yc(o,n,r):vc(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&E.Optional))$i(n);else return s}finally{lc()}}else if(typeof i=="number"){let s=null,a=mc(e,t),u=_t,c=r&E.Host?t[ne][Q]:null;for((a===-1||r&E.SkipSelf)&&(u=a===-1?os(e,t):t[a+8],u===_t||!$a(r,!1)?a=-1:(s=t[w],a=ur(u),t=cr(u,t)));a!==-1;){let l=t[w];if(Ba(i,a,l.data)){let d=Gp(a,t,n,s,r,c);if(d!==me)return d}u=t[a+8],u!==_t&&$a(r,t[w].data[a+8]===c)&&Ba(i,a,t)?(s=l,a=ur(u),t=cr(u,t)):a=-1}}return o}function Gp(e,t,n,r,o,i){let s=t[w],a=s.data[e+8],u=r==null?Tr(a)&&Wo:r!=s&&(a.type&3)!==0,c=o&E.Host&&i===a,l=Kn(a,s,n,u,c);return l!==null?ot(t,s,l,a):me}function Kn(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:c;for(let f=d;f<p;f++){let h=s[f];if(f<u&&n===h||f>=u&&h.type===n)return f}if(o){let f=s[u];if(f&&je(f)&&f.type===n)return u}return null}function ot(e,t,n,r){let o=e[n],i=t.data;if(Lp(o)){let s=o;s.resolving&&mf(gf(i[n]));let a=lr(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?G(s.injectImpl):null,l=sc(e,r,E.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Pp(n,i[n],t)}finally{c!==null&&G(c),lr(a),s.resolving=!1,lc()}}return o}function Wp(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Yt)?e[Yt]:void 0;return typeof t=="number"?t>=0?t&hc:qp:t}function Ba(e,t,n){let r=1<<e;return!!(n[t+(e>>gc)]&r)}function $a(e,t){return!(e&E.Self)&&!(e&E.Host&&t)}var Qe=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Dc(this._tNode,this._lView,t,Mr(r),n)}};function qp(){return new Qe($(),b())}function DM(e){return sn(()=>{let t=e.prototype.constructor,n=t[er]||Zo(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[er]||Zo(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Zo(e){return hu(e)?()=>{let t=Zo(H(e));return t&&t()}:Ke(e)}function Zp(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[v]&2048&&!(s[v]&512);){let a=Ic(i,s,n,r|E.Self,me);if(a!==me)return a;let u=i.parent;if(!u){let c=s[$u];if(c){let l=c.get(n,me,r);if(l!==me)return l}u=wc(s),s=s[kt]}i=u}return o}function wc(e){let t=e[w],n=t.type;return n===2?t.declTNode:n===1?e[Q]:null}function Yp(e){return zp($(),e)}function Ha(e,t=null,n=null,r){let o=Ec(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ec(e,t=null,n=null,r,o=new Set){let i=[n||W,Kf(e)];return r=r||(typeof e=="object"?void 0:U(e)),new Xt(i,t||qi(),r||null,o)}var nn=class e{static{this.THROW_IF_NOT_FOUND=Qt}static{this.NULL=new or}static create(t,n){if(Array.isArray(t))return Ha({name:""},n,t,"");{let r=t.name??"";return Ha({name:r},t.parent,t.providers,r)}}static{this.\u0275prov=P({token:e,providedIn:"any",factory:()=>Z(Eu)})}static{this.__NG_ELEMENT_ID__=-1}};var Qp=new A("");Qp.__NG_ELEMENT_ID__=e=>{let t=$();if(t===null)throw new x(204,!1);if(t.type&2)return t.value;if(e&E.Optional)return null;throw new x(204,!1)};var Kp="ngOriginalError";function Mo(e){return e[Kp]}var Cc=!0,bc=(()=>{class e{static{this.__NG_ELEMENT_ID__=Jp}static{this.__NG_ENV_ID__=n=>n}}return e})(),Yo=class extends bc{constructor(t){super(),this._lView=t}onDestroy(t){return Xu(this._lView,t),()=>mp(this._lView,t)}};function Jp(){return new Yo(b())}var Lr=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new $t(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=P({token:e,providedIn:"root",factory:()=>new e})}}return e})();var Qo=class extends ee{constructor(t=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=t,Bu()&&(this.destroyRef=T(bc,{optional:!0})??void 0,this.pendingTasks=T(Lr,{optional:!0})??void 0)}emit(t){let n=M(null);try{super.next(t)}finally{M(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let u=t;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof k&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},ce=Qo;function fr(...e){}function _c(e){let t,n;function r(){e=fr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ua(e){return queueMicrotask(()=>e()),()=>{e=fr}}var is="isAngularZone",pr=is+"_ID",Xp=0,q=class e{constructor(t){this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ce(!1),this.onMicrotaskEmpty=new ce(!1),this.onStable=new ce(!1),this.onError=new ce(!1);let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Cc}=t;if(typeof Zone>"u")throw new x(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,nh(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(is)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new x(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new x(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,eh,fr,fr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},eh={};function ss(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function th(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){_c(()=>{e.callbackScheduled=!1,Ko(e),e.isCheckStableRunning=!0,ss(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Ko(e)}function nh(e){let t=()=>{th(e)},n=Xp++;e._inner=e._inner.fork({name:"angular",properties:{[is]:!0,[pr]:n,[pr+n]:!0},onInvokeTask:(r,o,i,s,a,u)=>{if(rh(u))return r.invokeTask(i,s,a,u);try{return za(e),r.invokeTask(i,s,a,u)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Ga(e)}},onInvoke:(r,o,i,s,a,u,c)=>{try{return za(e),r.invoke(i,s,a,u,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!oh(u)&&t(),Ga(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Ko(e),ss(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Ko(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function za(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Ga(e){e._nesting--,ss(e)}var Jo=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ce,this.onMicrotaskEmpty=new ce,this.onStable=new ce,this.onError=new ce}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function rh(e){return Mc(e,"__ignore_ng_zone__")}function oh(e){return Mc(e,"__scheduler_tick__")}function Mc(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Ot=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Mo(t);for(;n&&Mo(n);)n=Mo(n);return n||null}},ih=new A("",{providedIn:"root",factory:()=>{let e=T(q),t=T(Ot);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function sh(){return Lt($(),b())}function Lt(e,t){return new jt(re(e,t))}var jt=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=sh}}return e})();function ah(e){return e instanceof jt?e.nativeElement:e}function uh(){return this._results[Symbol.iterator]()}var Xo=class e{get changes(){return this._changes??=new ce}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=e.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=uh)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Tf(t);(this._changesDetected=!Sf(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function xc(e){return(e.flags&128)===128}var Sc=new Map,ch=0;function lh(){return ch++}function dh(e){Sc.set(e[Sr],e)}function ei(e){Sc.delete(e[Sr])}var Wa="__ngContext__";function Ve(e,t){Pe(t)?(e[Wa]=t[Sr],dh(t)):e[Wa]=t}function Tc(e){return Ac(e[tn])}function Nc(e){return Ac(e[le])}function Ac(e){for(;e!==null&&!Ne(e);)e=e[le];return e}var ti;function IM(e){ti=e}function fh(){if(ti!==void 0)return ti;if(typeof document<"u")return document;throw new x(210,!1)}var wM=new A("",{providedIn:"root",factory:()=>ph}),ph="ng",hh=new A(""),gh=new A("",{providedIn:"platform",factory:()=>"unknown"});var EM=new A(""),CM=new A("",{providedIn:"root",factory:()=>fh().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var mh="h",yh="b";var vh=()=>null;function as(e,t,n=!1){return vh(e,t,n)}var Oc=!1,Dh=new A("",{providedIn:"root",factory:()=>Oc});var Un;function Ih(){if(Un===void 0&&(Un=null,Ma.trustedTypes))try{Un=Ma.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Un}function jr(e){return Ih()?.createHTML(e)||e}var xe=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${du})`}},ni=class extends xe{getTypeName(){return"HTML"}},ri=class extends xe{getTypeName(){return"Style"}},oi=class extends xe{getTypeName(){return"Script"}},ii=class extends xe{getTypeName(){return"URL"}},si=class extends xe{getTypeName(){return"ResourceURL"}};function Vr(e){return e instanceof xe?e.changingThisBreaksApplicationSecurity:e}function wh(e,t){let n=Eh(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${du})`)}return n===t}function Eh(e){return e instanceof xe&&e.getTypeName()||null}function bM(e){return new ni(e)}function _M(e){return new ri(e)}function MM(e){return new oi(e)}function xM(e){return new ii(e)}function SM(e){return new si(e)}function Ch(e){let t=new ui(e);return bh()?new ai(t):t}var ai=class{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(jr(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},ui=class{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=jr(t),n}};function bh(){try{return!!new window.DOMParser().parseFromString(jr(""),"text/html")}catch{return!1}}var _h=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Rc(e){return e=String(e),e.match(_h)?e:"unsafe:"+e}function Ae(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function un(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Fc=Ae("area,br,col,hr,img,wbr"),Pc=Ae("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),kc=Ae("rp,rt"),Mh=un(kc,Pc),xh=un(Pc,Ae("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),Sh=un(kc,Ae("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),qa=un(Fc,xh,Sh,Mh),Lc=Ae("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Th=Ae("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Nh=Ae("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Ah=un(Lc,Th,Nh),Oh=Ae("script,style,template"),ci=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=Ph(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Fh(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Za(t).toLowerCase();if(!qa.hasOwnProperty(n))return this.sanitizedSomething=!0,!Oh.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Ah.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;Lc[a]&&(u=Rc(u)),this.buf.push(" ",s,'="',Ya(u),'"')}return this.buf.push(">"),!0}endElement(t){let n=Za(t).toLowerCase();qa.hasOwnProperty(n)&&!Fc.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Ya(t))}};function Rh(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Fh(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw jc(t);return t}function Ph(e){let t=e.firstChild;if(t&&Rh(e,t))throw jc(t);return t}function Za(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function jc(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var kh=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Lh=/([^\#-~ |!])/g;function Ya(e){return e.replace(/&/g,"&amp;").replace(kh,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Lh,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var zn;function TM(e,t){let n=null;try{zn=zn||Ch(e);let r=t?String(t):"";n=zn.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=zn.getInertBodyElement(r)}while(r!==i);let a=new ci().sanitizeChildren(Qa(n)||n);return jr(a)}finally{if(n){let r=Qa(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function Qa(e){return"content"in e&&jh(e)?e.content:null}function jh(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Vc=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Vc||{});function NM(e){let t=Vh();return t?t.sanitize(Vc.URL,e)||"":wh(e,"URL")?Vr(e):Rc(Mt(e))}function Vh(){let e=b();return e&&e[ye].sanitizer}var Bh=/^>|^->|<!--|-->|--!>|<!-$/g,$h=/(<|>)/g,Hh="\u200B$1\u200B";function Uh(e){return e.replace(Bh,t=>t.replace($h,Hh))}function Bc(e){return e instanceof Function?e():e}var hr=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(hr||{}),zh;function us(e,t){return zh(e,t)}function Ct(e,t,n,r,o){if(r!=null){let i,s=!1;Ne(r)?i=r:Pe(r)&&(s=!0,r=r[Te]);let a=ve(r);e===0&&n!==null?o==null?Wc(t,n,a):gr(t,n,a,o||null,!0):e===1&&n!==null?gr(t,n,a,o||null,!0):e===2?og(t,a,s):e===3&&t.destroyNode(a),i!=null&&sg(t,e,i,n,o)}}function Gh(e,t){return e.createText(t)}function Wh(e,t,n){e.setValue(t,n)}function qh(e,t){return e.createComment(Uh(t))}function $c(e,t,n){return e.createElement(t,n)}function Zh(e,t){Hc(e,t),t[Te]=null,t[Q]=null}function Yh(e,t,n,r,o,i){r[Te]=o,r[Q]=t,$r(e,r,n,1,o,i)}function Hc(e,t){t[ye].changeDetectionScheduler?.notify(9),$r(e,t,t[F],2,null,null)}function Qh(e){let t=e[tn];if(!t)return xo(e[w],e);for(;t;){let n=null;if(Pe(t))n=t[tn];else{let r=t[X];r&&(n=r)}if(!n){for(;t&&!t[le]&&t!==e;)Pe(t)&&xo(t[w],t),t=t[B];t===null&&(t=e),Pe(t)&&xo(t[w],t),n=t&&t[le]}t=n}}function Kh(e,t,n,r){let o=X+r,i=n.length;r>0&&(n[o-1][le]=t),r<i-X?(t[le]=n[o],wu(n,X+r,t)):(n.push(t),t[le]=null),t[B]=n;let s=t[Xe];s!==null&&n!==s&&Uc(s,t);let a=t[Me];a!==null&&a.insertView(e),Uo(t),t[v]|=128}function Uc(e,t){let n=e[Nt],r=t[B];if(Pe(r))e[v]|=ar.HasTransplantedViews;else{let o=r[B][ne];t[ne]!==o&&(e[v]|=ar.HasTransplantedViews)}n===null?e[Nt]=[t]:n.push(t)}function cs(e,t){let n=e[Nt],r=n.indexOf(t);n.splice(r,1)}function li(e,t){if(e.length<=X)return;let n=X+t,r=e[n];if(r){let o=r[Xe];o!==null&&o!==e&&cs(o,r),t>0&&(e[n-1][le]=r[le]);let i=nr(e,X+t);Zh(r[w],r);let s=i[Me];s!==null&&s.detachView(i[w]),r[B]=null,r[le]=null,r[v]&=-129}return r}function zc(e,t){if(!(t[v]&256)){let n=t[F];n.destroyNode&&$r(e,t,n,3,null,null),Qh(t)}}function xo(e,t){if(t[v]&256)return;let n=M(null);try{t[v]&=-129,t[v]|=256,t[te]&&vo(t[te]),Xh(e,t),Jh(e,t),t[w].type===1&&t[F].destroy();let r=t[Xe];if(r!==null&&Ne(t[B])){r!==t[B]&&cs(r,t);let o=t[Me];o!==null&&o.detachView(e)}ei(t)}finally{M(n)}}function Jh(e,t){let n=e.cleanup,r=t[ir];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[ir]=null);let o=t[Fe];if(o!==null){t[Fe]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function Xh(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof rt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];ge(4,a,u);try{u.call(a)}finally{ge(5,a,u)}}else{ge(4,o,i);try{i.call(o)}finally{ge(5,o,i)}}}}}function Gc(e,t,n){return eg(e,t.parent,n)}function eg(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Te];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===Kt.None||i===Kt.Emulated)return null}return re(r,n)}}function gr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Wc(e,t,n){e.appendChild(t,n)}function Ka(e,t,n,r,o){r!==null?gr(e,t,n,r,o):Wc(e,t,n)}function qc(e,t){return e.parentNode(t)}function tg(e,t){return e.nextSibling(t)}function Zc(e,t,n){return rg(e,t,n)}function ng(e,t,n){return e.type&40?re(e,n):null}var rg=ng,Ja;function Br(e,t,n,r){let o=Gc(e,r,t),i=t[F],s=r.parent||t[Q],a=Zc(s,r,t);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)Ka(i,o,n[u],a,!1);else Ka(i,o,n,a,!1);Ja!==void 0&&Ja(i,r,t,n,o)}function qt(e,t){if(t!==null){let n=t.type;if(n&3)return re(t,e);if(n&4)return di(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return qt(e,r);{let o=e[t.index];return Ne(o)?di(-1,o):ve(o)}}else{if(n&128)return qt(e,t.next);if(n&32)return us(t,e)()||ve(e[t.index]);{let r=Yc(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=tt(e[ne]);return qt(o,r)}else return qt(e,t.next)}}}return null}function Yc(e,t){if(t!==null){let r=e[ne][Q],o=t.projection;return r.projection[o]}return null}function di(e,t){let n=X+e+1;if(n<t.length){let r=t[n],o=r[w].firstChild;if(o!==null)return qt(r,o)}return t[et]}function og(e,t,n){e.removeChild(null,t,n)}function ls(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],u=n.type;if(s&&t===0&&(a&&Ve(ve(a),r),n.flags|=2),(n.flags&32)!==32)if(u&8)ls(e,t,n.child,r,o,i,!1),Ct(t,e,o,a,i);else if(u&32){let c=us(n,r),l;for(;l=c();)Ct(t,e,o,l,i);Ct(t,e,o,a,i)}else u&16?Qc(e,t,r,n,o,i):Ct(t,e,o,a,i);n=s?n.projectionNext:n.next}}function $r(e,t,n,r,o,i){ls(n,r,e.firstChild,t,o,i,!1)}function ig(e,t,n){let r=t[F],o=Gc(e,n,t),i=n.parent||t[Q],s=Zc(i,n,t);Qc(r,0,t,n,o,s)}function Qc(e,t,n,r,o,i){let s=n[ne],u=s[Q].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];Ct(t,e,o,l,i)}else{let c=u,l=s[B];xc(r)&&(c.flags|=128),ls(e,t,c,l,o,i,!0)}}function sg(e,t,n,r,o){let i=n[et],s=ve(n);i!==s&&Ct(t,e,r,i,o);for(let a=X;a<n.length;a++){let u=n[a];$r(u[w],u,e,t,r,i)}}function ag(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:hr.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=hr.Important),e.setStyle(n,r,o,i))}}function ug(e,t,n){e.setAttribute(t,"style",n)}function Kc(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Jc(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Lo(e,t,r),o!==null&&Kc(e,t,o),i!==null&&ug(e,t,i)}var fe={};function AM(e=1){Xc(L(),b(),He()+e,!1)}function Xc(e,t,n,r){if(!r)if((t[v]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Yn(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Qn(t,i,0,n)}nt(n)}function oe(e,t=E.Default){let n=b();if(n===null)return Z(e,t);let r=$();return Dc(r,n,H(e),t)}function OM(){let e="invalid";throw new Error(e)}function el(e,t,n,r,o,i){let s=M(null);try{let a=null;o&ke.SignalBased&&(a=t[r][_e]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&ke.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Uu(t,a,r,i)}finally{M(s)}}function cg(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)nt(~o);else{let i=o,s=n[++r],a=n[++r];Sp(s,i);let u=t[i];a(2,u)}}}finally{nt(-1)}}function Hr(e,t,n,r,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[Te]=o,d[v]=r|4|128|8|64,(c!==null||e&&e[v]&2048)&&(d[v]|=2048),Ku(d),d[B]=d[kt]=e,d[de]=n,d[ye]=s||e&&e[ye],d[F]=a||e&&e[F],d[Tt]=u||e&&e[Tt]||null,d[Q]=i,d[Sr]=lh(),d[en]=l,d[$u]=c,d[ne]=t.type==2?e[ne]:d,d}function Vt(e,t,n,r,o){let i=e.data[t];if(i===null)i=lg(e,t,n,r,o),xp()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Ep();i.injectorIndex=s===null?-1:s.injectorIndex}return ut(i,!0),i}function lg(e,t,n,r,o){let i=rc(),s=Ki(),a=s?i:i&&i.parent,u=e.data[t]=mg(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=u),i!==null&&(s?i.child==null&&u.parent!==null&&(i.child=u):i.next===null&&(i.next=u,u.prev=i)),u}function tl(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function nl(e,t,n,r,o){let i=He(),s=r&2;try{nt(-1),s&&t.length>Y&&Xc(e,t,Y,!1),ge(s?2:0,o),n(r,o)}finally{nt(i),ge(s?3:1,o)}}function ds(e,t,n){if(Zi(t)){let r=M(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{M(r)}}}function fs(e,t,n){tc()&&(Eg(e,t,n,re(n,t)),(n.flags&64)===64&&sl(e,t,n))}function ps(e,t,n=re){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function rl(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=hs(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function hs(e,t,n,r,o,i,s,a,u,c,l){let d=Y+r,p=d+o,f=dg(d,p),h=typeof c=="function"?c():c;return f[w]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:h,incompleteFirstPass:!1,ssrId:l}}function dg(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:fe);return n}function fg(e,t,n,r){let i=r.get(Dh,Oc)||n===Kt.ShadowDom,s=e.selectRootElement(t,i);return pg(s),s}function pg(e){hg(e)}var hg=()=>null;function gg(e,t,n,r){let o=cl(t);o.push(n),e.firstCreatePass&&ll(e).push(r,o.length-1)}function mg(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return nc()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Xa(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,u=ke.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?eu(r,n,c,a,u):eu(r,n,c,a)}return r}function eu(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function yg(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=r;l<o;l++){let d=i[l],p=n?n.get(d):null,f=p?p.inputs:null,h=p?p.outputs:null;u=Xa(0,d.inputs,l,u,f),c=Xa(1,d.outputs,l,c,h);let y=u!==null&&s!==null&&!Gi(t)?Rg(u,l,s):null;a.push(y)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function vg(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function ol(e,t,n,r,o,i,s,a){let u=re(t,n),c=t.inputs,l;!a&&c!=null&&(l=c[r])?(ms(e,n,l,r,o),Tr(t)&&Dg(n,t.index)):t.type&3?(r=vg(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(u,r,o)):t.type&12}function Dg(e,t){let n=$e(t,e);n[v]&16||(n[v]|=64)}function gs(e,t,n,r){if(tc()){let o=r===null?null:{"":-1},i=bg(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&il(e,t,n,s,o,a),o&&_g(n,r,o)}n.mergedAttrs=Jt(n.mergedAttrs,n.attrs)}function il(e,t,n,r,o,i){for(let c=0;c<r.length;c++)qo(dr(n,t),e,r[c].type);xg(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let l=r[c];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,u=tl(e,t,r.length,null);for(let c=0;c<r.length;c++){let l=r[c];n.mergedAttrs=Jt(n.mergedAttrs,l.hostAttrs),Sg(e,n,t,u,l),Mg(u,l,o),l.contentQueries!==null&&(n.flags|=4),(l.hostBindings!==null||l.hostAttrs!==null||l.hostVars!==0)&&(n.flags|=64);let d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}yg(e,n,i)}function Ig(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;wg(s)!=a&&s.push(a),s.push(n,r,i)}}function wg(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Eg(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;Tr(n)&&Tg(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||dr(n,t),Ve(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let u=e.data[a],c=ot(t,e,a,n);if(Ve(c,t),s!==null&&Og(t,a-o,c,u,n,s),je(u)){let l=$e(n.index,t);l[de]=ot(t,e,a,n)}}}function sl(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Tp();try{nt(i);for(let a=r;a<o;a++){let u=e.data[a],c=t[a];zo(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&Cg(u,c)}}finally{nt(-1),zo(s)}}function Cg(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function bg(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(xu(t,s.selectors,!1))if(r||(r=[]),je(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let u=a.length;fi(e,t,u)}else r.unshift(s),fi(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function fi(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function _g(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new x(-301,!1);r.push(t[o],i)}}}function Mg(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;je(t)&&(n[""]=e)}}function xg(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Sg(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Ke(o.type,!0)),s=new rt(i,je(o),oe);e.blueprint[r]=s,n[r]=s,Ig(e,t,r,tl(e,n,o.hostVars,fe),o)}function Tg(e,t,n){let r=re(t,e),o=rl(n),i=e[ye].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=Ur(e,Hr(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function Ng(e,t,n,r,o,i){let s=re(e,t);Ag(t[F],s,i,e.value,n,r,o)}function Ag(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Mt(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Og(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];el(r,n,u,c,l,d)}}function Rg(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function al(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function ul(e,t){let n=e.contentQueries;if(n!==null){let r=M(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];es(i),a.contentQueries(2,t[s],s)}}}finally{M(r)}}}function Ur(e,t){return e[tn]?e[ka][le]=t:e[tn]=t,e[ka]=t,t}function pi(e,t,n){es(0);let r=M(null);try{t(e,n)}finally{M(r)}}function cl(e){return e[ir]??=[]}function ll(e){return e.cleanup??=[]}function dl(e,t){let n=e[Tt],r=n?n.get(Ot,null):null;r&&r.handleError(t)}function ms(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],c=t[s],l=e.data[s];el(l,c,r,a,u,o)}}function fl(e,t,n){let r=Zu(t,e);Wh(e[F],r,n)}function Fg(e,t){let n=$e(t,e),r=n[w];Pg(r,n);let o=n[Te];o!==null&&n[en]===null&&(n[en]=as(o,n[Tt])),ys(r,n,n[de])}function Pg(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function ys(e,t,n){ts(t);try{let r=e.viewQuery;r!==null&&pi(1,r,n);let o=e.template;o!==null&&nl(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Me]?.finishViewCreation(e),e.staticContentQueries&&ul(e,t),e.staticViewQueries&&pi(2,e.viewQuery,n);let i=e.components;i!==null&&kg(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[v]&=-5,ns()}}function kg(e,t){for(let n=0;n<t.length;n++)Fg(e,t[n])}function pl(e,t,n,r){let o=M(null);try{let i=t.tView,a=e[v]&4096?4096:16,u=Hr(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];u[Xe]=c;let l=e[Me];return l!==null&&(u[Me]=l.createEmbeddedView(i)),ys(i,u,n),u}finally{M(o)}}function hi(e,t){return!t||t.firstChild===null||xc(e)}function hl(e,t,n,r=!0){let o=t[w];if(Kh(o,t,e,n),r){let s=di(n,e),a=t[F],u=qc(a,e[et]);u!==null&&Yh(o,e[Q],a,t,u,s)}let i=t[en];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function mr(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ve(i)),Ne(i)&&Lg(i,r);let s=n.type;if(s&8)mr(e,t,n.child,r);else if(s&32){let a=us(n,t),u;for(;u=a();)r.push(u)}else if(s&16){let a=Yc(t,n);if(Array.isArray(a))r.push(...a);else{let u=tt(t[ne]);mr(u[w],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Lg(e,t){for(let n=X;n<e.length;n++){let r=e[n],o=r[w].firstChild;o!==null&&mr(r[w],r,o,t)}e[et]!==e[Te]&&t.push(e[et])}var gl=[];function jg(e){return e[te]??Vg(e)}function Vg(e){let t=gl.pop()??Object.create($g);return t.lView=e,t}function Bg(e){e.lView[te]!==e&&(e.lView=null,gl.push(e))}var $g=we(Ie({},Wt),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Or(e.lView)},consumerOnSignalRead(){this.lView[te]=this}});function Hg(e){let t=e[te]??Object.create(Ug);return t.lView=e,t}var Ug=we(Ie({},Wt),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let t=tt(e.lView);for(;t&&!ml(t[w]);)t=tt(t);t&&Ju(t)},consumerOnSignalRead(){this.lView[te]=this}});function ml(e){return e.type!==2}var zg=100;function yl(e,t=!0,n=0){let r=e[ye],o=r.rendererFactory,i=!1;i||o.begin?.();try{Gg(e,n)}catch(s){throw t&&dl(e,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function Gg(e,t){let n=oc();try{ja(!0),gi(e,t);let r=0;for(;Ar(e);){if(r===zg)throw new x(103,!1);r++,gi(e,1)}}finally{ja(n)}}function Wg(e,t,n,r){let o=t[v];if((o&256)===256)return;let i=!1,s=!1;!i&&t[ye].inlineEffectRunner?.flush(),ts(t);let a=!0,u=null,c=null;i||(ml(e)?(c=jg(t),u=Vn(c)):fa()===null?(a=!1,c=Hg(t),u=Vn(c)):t[te]&&(vo(t[te]),t[te]=null));try{Ku(t),Mp(e.bindingStartIndex),n!==null&&nl(e,t,n,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&Yn(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Qn(t,f,0,null),Co(t,0)}if(s||qg(t),vl(t,0),e.contentQueries!==null&&ul(e,t),!i)if(l){let f=e.contentCheckHooks;f!==null&&Yn(t,f)}else{let f=e.contentHooks;f!==null&&Qn(t,f,1),Co(t,1)}cg(e,t);let d=e.components;d!==null&&Il(t,d,0);let p=e.viewQuery;if(p!==null&&pi(2,p,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&Yn(t,f)}else{let f=e.viewHooks;f!==null&&Qn(t,f,2),Co(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Eo]){for(let f of t[Eo])f();t[Eo]=null}i||(t[v]&=-73)}catch(l){throw i||Or(t),l}finally{c!==null&&(mo(c,u),a&&Bg(c)),ns()}}function vl(e,t){for(let n=Tc(e);n!==null;n=Nc(n))for(let r=X;r<n.length;r++){let o=n[r];Dl(o,t)}}function qg(e){for(let t=Tc(e);t!==null;t=Nc(t)){if(!(t[v]&ar.HasTransplantedViews))continue;let n=t[Nt];for(let r=0;r<n.length;r++){let o=n[r];Ju(o)}}}function Zg(e,t,n){let r=$e(t,e);Dl(r,n)}function Dl(e,t){Qi(e)&&gi(e,t)}function gi(e,t){let r=e[w],o=e[v],i=e[te],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&yo(i)),s||=!1,i&&(i.dirty=!1),e[v]&=-9217,s)Wg(r,e,r.template,e[de]);else if(o&8192){vl(e,1);let a=r.components;a!==null&&Il(e,a,1)}}function Il(e,t,n){for(let r=0;r<t.length;r++)Zg(e,t[r],n)}function vs(e,t){let n=oc()?64:1088;for(e[ye].changeDetectionScheduler?.notify(t);e;){e[v]|=n;let r=tt(e);if($o(e)&&!r)return e;e=r}return null}var it=class{get rootNodes(){let t=this._lView,n=t[w];return mr(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[de]}set context(t){this._lView[de]=t}get destroyed(){return(this._lView[v]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[B];if(Ne(t)){let n=t[sr],r=n?n.indexOf(this):-1;r>-1&&(li(t,r),nr(n,r))}this._attachedToViewContainer=!1}zc(this._lView[w],this._lView)}onDestroy(t){Xu(this._lView,t)}markForCheck(){vs(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[v]&=-129}reattach(){Uo(this._lView),this._lView[v]|=128}detectChanges(){this._lView[v]|=1024,yl(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new x(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=$o(this._lView),n=this._lView[Xe];n!==null&&!t&&cs(n,this._lView),Hc(this._lView[w],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new x(902,!1);this._appRef=t;let n=$o(this._lView),r=this._lView[Xe];r!==null&&!n&&Uc(r,this._lView),Uo(this._lView)}},st=(()=>{class e{static{this.__NG_ELEMENT_ID__=Kg}}return e})(),Yg=st,Qg=class extends Yg{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=pl(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new it(o)}};function Kg(){return zr($(),b())}function zr(e,t){return e.type&4?new Qg(t,e,Lt(e,t)):null}var FM=new RegExp(`^(\\d+)*(${yh}|${mh})*(.*)`);var Jg=()=>null;function mi(e,t){return Jg(e,t)}var Rt=class{},Ds=new A("",{providedIn:"root",factory:()=>!1});var wl=new A(""),El=new A(""),yi=class{},yr=class{};function Xg(e){let t=Error(`No component factory found for ${U(e)}.`);return t[em]=e,t}var em="ngComponent";var vi=class{resolveComponentFactory(t){throw Xg(t)}},Ft=class{static{this.NULL=new vi}},vr=class{},Cl=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>tm()}}return e})();function tm(){let e=b(),t=$(),n=$e(t.index,e);return(Pe(n)?n:e)[F]}var nm=(()=>{class e{static{this.\u0275prov=P({token:e,providedIn:"root",factory:()=>null})}}return e})();function Dr(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ro(o,a);else if(i==2){let u=a,c=t[++s];r=Ro(r,u+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var Ir=class extends Ft{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Je(t);return new rn(n,this.ngModule)}};function tu(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let o=e[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:ke.None;t?n.push({propName:s,templateName:r,isSignal:(a&ke.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function rm(e){let t=e.toLowerCase();return t==="svg"?qu:t==="math"?fp:null}var rn=class extends yr{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=tu(t.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return tu(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=zf(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=M(null);try{o=o||this.ngModule;let s=o instanceof Le?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Go(t,s):t,u=a.get(vr,null);if(u===null)throw new x(407,!1);let c=a.get(nm,null),l=a.get(Rt,null),d={rendererFactory:u,sanitizer:c,inlineEffectRunner:null,changeDetectionScheduler:l},p=u.createRenderer(null,this.componentDef),f=this.componentDef.selectors[0][0]||"div",h=r?fg(p,r,this.componentDef.encapsulation,a):$c(p,f,rm(f)),y=512;this.componentDef.signals?y|=4096:this.componentDef.onPush||(y|=16);let N=null;h!==null&&(N=as(h,a,!0));let C=hs(0,null,null,1,0,null,null,null,null,null,null),R=Hr(null,C,null,y,null,null,d,p,a,null,N);ts(R);let De,ie,dt=null;try{let K=this.componentDef,ft,qr=null;K.findHostDirectiveDefs?(ft=[],qr=new Map,K.findHostDirectiveDefs(K,ft,qr),ft.push(K)):ft=[K];let rd=om(R,h);dt=im(rd,h,K,ft,R,d,p),ie=Yu(C,Y),h&&um(p,K,h,r),n!==void 0&&cm(ie,this.ngContentSelectors,n),De=am(dt,K,ft,qr,R,[lm]),ys(C,R,null)}catch(K){throw dt!==null&&ei(dt),ei(R),K}finally{ns()}return new Di(this.componentType,De,Lt(ie,R),R,ie)}finally{M(i)}}},Di=class extends yi{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new it(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;ms(i[w],i,o,t,n),this.previousInputValues.set(t,n);let s=$e(this._tNode.index,i);vs(s,1)}}get injector(){return new Qe(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function om(e,t){let n=e[w],r=Y;return e[r]=t,Vt(n,r,2,"#host",null)}function im(e,t,n,r,o,i,s){let a=o[w];sm(r,e,t,s);let u=null;t!==null&&(u=as(t,o[Tt]));let c=i.rendererFactory.createRenderer(t,n),l=16;n.signals?l=4096:n.onPush&&(l=64);let d=Hr(o,rl(n),null,l,o[e.index],e,i,c,null,null,u);return a.firstCreatePass&&fi(a,e,r.length-1),Ur(o,d),o[e.index]=d}function sm(e,t,n,r){for(let o of e)t.mergedAttrs=Jt(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(Dr(t,t.mergedAttrs,!0),n!==null&&Jc(r,n,t))}function am(e,t,n,r,o,i){let s=$(),a=o[w],u=re(s,o);il(a,o,s,n,null,r);for(let l=0;l<n.length;l++){let d=s.directiveStart+l,p=ot(o,a,d,s);Ve(p,o)}sl(a,o,s),u&&Ve(u,o);let c=ot(o,a,s.directiveStart+s.componentOffset,s);if(e[de]=o[de]=c,i!==null)for(let l of i)l(c,t);return ds(a,s,o),c}function um(e,t,n,r){if(r)Lo(e,n,["ng-version","18.2.13"]);else{let{attrs:o,classes:i}=Gf(t.selectors[0]);o&&Lo(e,n,o),i&&i.length>0&&Kc(e,n,i.join(" "))}}function cm(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function lm(){let e=$();kr(b()[w],e)}var ct=(()=>{class e{static{this.__NG_ELEMENT_ID__=dm}}return e})();function dm(){let e=$();return _l(e,b())}var fm=ct,bl=class extends fm{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Lt(this._hostTNode,this._hostLView)}get injector(){return new Qe(this._hostTNode,this._hostLView)}get parentInjector(){let t=os(this._hostTNode,this._hostLView);if(pc(t)){let n=cr(t,this._hostLView),r=ur(t),o=n[w].data[r+8];return new Qe(o,n)}else return new Qe(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=nu(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-X}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=mi(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,hi(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!up(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let u=s?t:new rn(Je(t)),c=r||this.parentInjector;if(!i&&u.ngModule==null){let y=(s?c:this.parentInjector).get(Le,null);y&&(i=y)}let l=Je(u.componentType??{}),d=mi(this._lContainer,l?.id??null),p=d?.firstChild??null,f=u.create(c,o,p,i);return this.insertImpl(f.hostView,a,hi(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(hp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[B],c=new bl(u,u[Q],u[B]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return hl(s,o,i,r),t.attachToViewContainerRef(),wu(So(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=nu(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=li(this._lContainer,n);r&&(nr(So(this._lContainer),n),zc(r[w],r))}detach(t){let n=this._adjustIndex(t,-1),r=li(this._lContainer,n);return r&&nr(So(this._lContainer),n)!=null?new it(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function nu(e){return e[sr]}function So(e){return e[sr]||(e[sr]=[])}function _l(e,t){let n,r=t[e.index];return Ne(r)?n=r:(n=al(r,t,null,e),t[e.index]=n,Ur(t,n)),hm(n,t,e,r),new bl(n,e,t)}function pm(e,t){let n=e[F],r=n.createComment(""),o=re(t,e),i=qc(n,o);return gr(n,i,r,tg(n,o),!1),r}var hm=ym,gm=()=>!1;function mm(e,t,n){return gm(e,t,n)}function ym(e,t,n,r){if(e[et])return;let o;n.type&8?o=ve(r):o=pm(t,n),e[et]=o}var Ii=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},wi=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Is(t,n).matches!==null&&this.queries[n].setDirty()}},Ei=class{constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=_m(t):this.predicate=t}},Ci=class e{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},bi=class e{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,vm(n,i)),this.matchTNodeWithReadOption(t,n,Kn(n,t,i,!1,!1))}else r===st?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Kn(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===jt||o===ct||o===st&&n.type&4)this.addMatch(n.index,-2);else{let i=Kn(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function vm(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function Dm(e,t){return e.type&11?Lt(e,t):e.type&4?zr(e,t):null}function Im(e,t,n,r){return n===-1?Dm(t,e):n===-2?wm(e,t,r):ot(e,e[w],n,t)}function wm(e,t,n){if(n===jt)return Lt(t,e);if(n===st)return zr(t,e);if(n===ct)return _l(t,e)}function Ml(e,t,n,r){let o=t[Me].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push(Im(t,l,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function _i(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Ml(e,t,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=X;d<l.length;d++){let p=l[d];p[Xe]===p[B]&&_i(p[w],p,c,r)}if(l[Nt]!==null){let d=l[Nt];for(let p=0;p<d.length;p++){let f=d[p];_i(f[w],f,c,r)}}}}}return r}function Em(e,t){return e[Me].queries[t].queryList}function Cm(e,t,n){let r=new Xo((n&4)===4);return gg(e,t,r,r.destroy),(t[Me]??=new wi).queries.push(new Ii(r))-1}function bm(e,t,n){let r=L();return r.firstCreatePass&&(Mm(r,new Ei(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Cm(r,b(),t)}function _m(e){return e.split(",").map(t=>t.trim())}function Mm(e,t,n){e.queries===null&&(e.queries=new Ci),e.queries.track(new bi(t,n))}function Is(e,t){return e.queries.getByIndex(t)}function xm(e,t){let n=e[w],r=Is(n,t);return r.crossesNgTemplate?_i(n,e,t,[]):Ml(n,e,r,t)}var ru=new Set;function cn(e){ru.has(e)||(ru.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function Sm(e){return typeof e=="function"&&e[_e]!==void 0}function kM(e,t){cn("NgSignals");let n=Ca(e),r=n[_e];return t?.equal&&(r.equal=t.equal),n.set=o=>Do(r,o),n.update=o=>ba(r,o),n.asReadonly=Tm.bind(n),n}function Tm(){let e=this[_e];if(e.readonlyFn===void 0){let t=()=>this();t[_e]=e,e.readonlyFn=t}return e.readonlyFn}function xl(e){return Sm(e)&&typeof e.set=="function"}function Nm(e){return Object.getPrototypeOf(e.prototype).constructor}function Am(e){let t=Nm(e.type),n=!0,r=[e];for(;t;){let o;if(je(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new x(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Gn(e.inputs),s.inputTransforms=Gn(e.inputTransforms),s.declaredInputs=Gn(e.declaredInputs),s.outputs=Gn(e.outputs);let a=o.hostBindings;a&&km(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&Fm(e,u),c&&Pm(e,c),Om(e,o),af(e.outputs,o.outputs),je(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Am&&(n=!1)}}t=Object.getPrototypeOf(t)}Rm(r)}function Om(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function Rm(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Jt(o.hostAttrs,n=Jt(n,o.hostAttrs))}}function Gn(e){return e===xt?{}:e===W?[]:e}function Fm(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Pm(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function km(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Lm(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var Be=class{},Mi=class{};var xi=class extends Be{constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Ir(this);let i=Ou(t);this._bootstrapComponents=Bc(i.bootstrap),this._r3Injector=Ec(t,n,[{provide:Be,useValue:this},{provide:Ft,useValue:this.componentFactoryResolver},...r],U(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Si=class extends Mi{constructor(t){super(),this.moduleType=t}create(t){return new xi(this.moduleType,t,[])}};var wr=class extends Be{constructor(t){super(),this.componentFactoryResolver=new Ir(this),this.instance=null;let n=new Xt([...t.providers,{provide:Be,useValue:this},{provide:Ft,useValue:this.componentFactoryResolver}],t.parent||qi(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function jm(e,t,n=null){return new wr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}function Sl(e){return Bm(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Vm(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Bm(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function $m(e,t,n){return e[t]=n}function Se(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Hm(e,t,n,r){let o=Se(e,t,n);return Se(e,t+1,r)||o}function Um(e){return(e.flags&32)===32}function zm(e,t,n,r,o,i,s,a,u){let c=t.consts,l=Vt(t,e,4,s||null,a||null);gs(t,n,l,At(c,u)),kr(t,l);let d=l.tView=hs(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function Tl(e,t,n,r,o,i,s,a,u,c){let l=n+Y,d=t.firstCreatePass?zm(l,t,e,r,o,i,s,a,u):t.data[l];ut(d,!1);let p=Wm(t,e,d,n);Fr()&&Br(t,e,p,d),Ve(p,e);let f=al(p,e,p,d);return e[l]=f,Ur(e,f),mm(f,d,e),Nr(d)&&fs(t,e,d),u!=null&&ps(e,d,c),d}function Gm(e,t,n,r,o,i,s,a){let u=b(),c=L(),l=At(c.consts,i);return Tl(u,c,e,t,n,r,o,l,s,a),Gm}var Wm=qm;function qm(e,t,n,r){return Pr(!0),t[F].createComment("")}var Zt=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Zt||{}),Zm=(()=>{class e{constructor(){this.impl=null}execute(){this.impl?.execute()}static{this.\u0275prov=P({token:e,providedIn:"root",factory:()=>new e})}}return e})(),ou=class e{constructor(){this.ngZone=T(q),this.scheduler=T(Rt),this.errorHandler=T(Ot,{optional:!0}),this.sequences=new Set,this.deferredRegistrations=new Set,this.executing=!1}static{this.PHASES=[Zt.EarlyRead,Zt.Write,Zt.MixedReadWrite,Zt.Read]}execute(){this.executing=!0;for(let t of e.PHASES)for(let n of this.sequences)if(!(n.erroredOrDestroyed||!n.hooks[t]))try{n.pipelinedValue=this.ngZone.runOutsideAngular(()=>n.hooks[t](n.pipelinedValue))}catch(r){n.erroredOrDestroyed=!0,this.errorHandler?.handleError(r)}this.executing=!1;for(let t of this.sequences)t.afterRun(),t.once&&(this.sequences.delete(t),t.destroy());for(let t of this.deferredRegistrations)this.sequences.add(t);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear()}register(t){this.executing?this.deferredRegistrations.add(t):(this.sequences.add(t),this.scheduler.notify(6))}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}static{this.\u0275prov=P({token:e,providedIn:"root",factory:()=>new e})}};function Ym(e,t,n,r){let o=b(),i=Rr();if(Se(o,i,t)){let s=L(),a=rs();Ng(a,o,e,t,n,r)}return Ym}function Qm(e,t,n,r){return Se(e,Rr(),n)?t+Mt(n)+r:fe}function Km(e,t,n,r,o,i){let s=_p(),a=Hm(e,s,n,o);return Xi(2),a?t+Mt(n)+r+Mt(o)+i:fe}function Wn(e,t){return e<<17|t<<2}function at(e){return e>>17&32767}function Jm(e){return(e&2)==2}function Xm(e,t){return e&131071|t<<17}function Ti(e){return e|2}function Pt(e){return(e&131068)>>2}function To(e,t){return e&-131069|t<<2}function ey(e){return(e&1)===1}function Ni(e){return e|1}function ty(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=at(s),u=Pt(s);e[r]=n;let c=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||an(d,l)>0)&&(c=!0)}else l=n;if(o)if(u!==0){let p=at(e[a+1]);e[r+1]=Wn(p,a),p!==0&&(e[p+1]=To(e[p+1],r)),e[a+1]=Xm(e[a+1],r)}else e[r+1]=Wn(a,0),a!==0&&(e[a+1]=To(e[a+1],r)),a=r;else e[r+1]=Wn(u,0),a===0?a=r:e[u+1]=To(e[u+1],r),u=r;c&&(e[r+1]=Ti(e[r+1])),iu(e,l,r,!0),iu(e,l,r,!1),ny(t,l,e,r,i),s=Wn(a,u),i?t.classBindings=s:t.styleBindings=s}function ny(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&an(i,t)>=0&&(n[r+1]=Ni(n[r+1]))}function iu(e,t,n,r){let o=e[n+1],i=t===null,s=r?at(o):Pt(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];ry(u,t)&&(a=!0,e[s+1]=r?Ni(c):Ti(c)),s=r?at(c):Pt(c)}a&&(e[n+1]=r?Ti(o):Ni(o))}function ry(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?an(e,t)>=0:!1}var ue={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function oy(e){return e.substring(ue.key,ue.keyEnd)}function iy(e){return sy(e),Nl(e,Al(e,0,ue.textEnd))}function Nl(e,t){let n=ue.textEnd;return n===t?-1:(t=ue.keyEnd=ay(e,ue.key=t,n),Al(e,t,n))}function sy(e){ue.key=0,ue.keyEnd=0,ue.value=0,ue.valueEnd=0,ue.textEnd=e.length}function Al(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function ay(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function uy(e,t,n){let r=b(),o=Rr();if(Se(r,o,t)){let i=L(),s=rs();ol(i,s,r,e,t,r[F],n,!1)}return uy}function Ai(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";ms(e,n,i[s],s,r)}function Ol(e,t,n){return Rl(e,t,n,!1),Ol}function cy(e,t){return Rl(e,t,null,!0),cy}function LM(e){dy(yy,ly,e,!0)}function ly(e,t){for(let n=iy(t);n>=0;n=Nl(t,n))zi(e,oy(t),!0)}function Rl(e,t,n,r){let o=b(),i=L(),s=Xi(2);if(i.firstUpdatePass&&Pl(i,e,s,r),t!==fe&&Se(o,s,t)){let a=i.data[He()];kl(i,a,o,o[F],e,o[s+1]=Dy(t,n),r,s)}}function dy(e,t,n,r){let o=L(),i=Xi(2);o.firstUpdatePass&&Pl(o,null,i,r);let s=b();if(n!==fe&&Se(s,i,n)){let a=o.data[He()];if(Ll(a,r)&&!Fl(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=Ro(u,n||"")),Ai(o,a,s,n,r)}else vy(o,a,s,s[F],s[i+1],s[i+1]=my(e,t,n),r,i)}}function Fl(e,t){return t>=e.expandoStartIndex}function Pl(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[He()],s=Fl(e,n);Ll(i,r)&&t===null&&!s&&(t=!1),t=fy(o,i,t,r),ty(o,i,t,n,s,r)}}function fy(e,t,n,r){let o=Np(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=No(null,e,t,n,r),n=on(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=No(o,e,t,n,r),i===null){let u=py(e,t,r);u!==void 0&&Array.isArray(u)&&(u=No(null,e,t,u[1],r),u=on(u,t.attrs,r),hy(e,t,r,u))}else i=gy(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function py(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Pt(r)!==0)return e[at(r)]}function hy(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[at(o)]=r}function gy(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=on(r,s,n)}return on(r,t.attrs,n)}function No(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=on(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function on(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),zi(e,s,n?!0:t[++i]))}return e===void 0?null:e}function my(e,t,n){if(n==null||n==="")return W;let r=[],o=Vr(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function yy(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&zi(e,r,n)}function vy(e,t,n,r,o,i,s,a){o===fe&&(o=W);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let p=u<o.length?o[u+1]:void 0,f=c<i.length?i[c+1]:void 0,h=null,y;l===d?(u+=2,c+=2,p!==f&&(h=d,y=f)):d===null||l!==null&&l<d?(u+=2,h=l):(c+=2,h=d,y=f),h!==null&&kl(e,t,n,r,h,y,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function kl(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=ey(c)?su(u,t,n,o,Pt(c),s):void 0;if(!Er(l)){Er(i)||Jm(c)&&(i=su(u,null,n,o,a,s));let d=Zu(He(),n);ag(r,s,d,o,i)}}function su(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,p=n[o+1];p===fe&&(p=d?W:void 0);let f=d?Io(p,r):l===r?p:void 0;if(c&&!Er(f)&&(f=Io(u,r)),Er(f)&&(a=f,s))return a;let h=e[o+1];o=s?at(h):Pt(h)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=Io(u,r))}return a}function Er(e){return e!==void 0}function Dy(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=U(Vr(e)))),e}function Ll(e,t){return(e.flags&(t?8:16))!==0}function Iy(e,t,n,r,o,i){let s=t.consts,a=At(s,o),u=Vt(t,e,2,r,a);return gs(t,n,u,At(s,i)),u.attrs!==null&&Dr(u,u.attrs,!1),u.mergedAttrs!==null&&Dr(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function jl(e,t,n,r){let o=b(),i=L(),s=Y+e,a=o[F],u=i.firstCreatePass?Iy(s,i,o,t,n,r):i.data[s],c=Ey(i,o,u,a,t,e);o[s]=c;let l=Nr(u);return ut(u,!0),Jc(a,c,u),!Um(u)&&Fr()&&Br(i,o,c,u),yp()===0&&Ve(c,o),vp(),l&&(fs(i,o,u),ds(i,u,o)),r!==null&&ps(o,u),jl}function Vl(){let e=$();Ki()?Ji():(e=e.parent,ut(e,!1));let t=e;Ip(t)&&wp(),Dp();let n=L();return n.firstCreatePass&&(kr(n,e),Zi(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&jp(t)&&Ai(n,t,b(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Vp(t)&&Ai(n,t,b(),t.stylesWithoutHost,!1),Vl}function wy(e,t,n,r){return jl(e,t,n,r),Vl(),wy}var Ey=(e,t,n,r,o,i)=>(Pr(!0),$c(r,o,Fp()));function Cy(e,t,n,r,o){let i=t.consts,s=At(i,r),a=Vt(t,e,8,"ng-container",s);s!==null&&Dr(a,s,!0);let u=At(i,o);return gs(t,n,a,u),t.queries!==null&&t.queries.elementStart(t,a),a}function Bl(e,t,n){let r=b(),o=L(),i=e+Y,s=o.firstCreatePass?Cy(i,o,r,t,n):o.data[i];ut(s,!0);let a=_y(o,r,s,e);return r[i]=a,Fr()&&Br(o,r,a,s),Ve(a,r),Nr(s)&&(fs(o,r,s),ds(o,s,r)),n!=null&&ps(r,s),Bl}function $l(){let e=$(),t=L();return Ki()?Ji():(e=e.parent,ut(e,!1)),t.firstCreatePass&&(kr(t,e),Zi(e)&&t.queries.elementEnd(e)),$l}function by(e,t,n){return Bl(e,t,n),$l(),by}var _y=(e,t,n,r)=>(Pr(!0),qh(t[F],""));function jM(){return b()}var Cr="en-US";var My=Cr;function xy(e){typeof e=="string"&&(My=e.toLowerCase().replace(/_/g,"-"))}var Sy=(e,t,n)=>{};function Ty(e,t,n,r){let o=b(),i=L(),s=$();return Hl(i,o,o[F],s,e,t,r),Ty}function Ny(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[ir],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function Hl(e,t,n,r,o,i,s){let a=Nr(r),c=e.firstCreatePass&&ll(e),l=t[de],d=cl(t),p=!0;if(r.type&3||s){let y=re(r,t),N=s?s(y):y,C=d.length,R=s?ie=>s(ve(ie[r.index])):r.index,De=null;if(!s&&a&&(De=Ny(e,t,o,r.index)),De!==null){let ie=De.__ngLastListenerFn__||De;ie.__ngNextListenerFn__=i,De.__ngLastListenerFn__=i,p=!1}else{i=uu(r,t,l,i),Sy(y,o,i);let ie=n.listen(N,o,i);d.push(i,ie),c&&c.push(o,R,C,C+1)}}else i=uu(r,t,l,i);let f=r.outputs,h;if(p&&f!==null&&(h=f[o])){let y=h.length;if(y)for(let N=0;N<y;N+=2){let C=h[N],R=h[N+1],dt=t[C][R].subscribe(i),K=d.length;d.push(i,dt),c&&c.push(o,r.index,K,-(K+1))}}}function au(e,t,n,r){let o=M(null);try{return ge(6,t,n),n(r)!==!1}catch(i){return dl(e,i),!1}finally{ge(7,t,n),M(o)}}function uu(e,t,n,r){return function o(i){if(i===Function)return r;let s=e.componentOffset>-1?$e(e.index,t):t;vs(s,5);let a=au(t,n,r,i),u=o.__ngNextListenerFn__;for(;u;)a=au(t,n,u,i)&&a,u=u.__ngNextListenerFn__;return a}}function VM(e=1){return Op(e)}function Ay(e,t){let n=null,r=Vf(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?xu(e,i,!0):Hf(r,i))return o}return n}function BM(e){let t=b()[ne][Q];if(!t.projection){let n=e?e.length:1,r=t.projection=Nf(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Ay(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function $M(e,t=0,n,r,o,i){let s=b(),a=L(),u=r?e+1:null;u!==null&&Tl(s,a,u,r,o,i,null,n);let c=Vt(a,Y+e,16,null,n||null);c.projection===null&&(c.projection=t),Ji();let d=!s[en]||nc();s[ne][Q].projection[c.projection]===null&&u!==null?Oy(s,a,u):d&&(c.flags&32)!==32&&ig(a,s,c)}function Oy(e,t,n){let r=Y+n,o=t.data[r],i=e[r],s=mi(i,o.tView.ssrId),a=pl(e,o,void 0,{dehydratedView:s});hl(i,a,0,hi(o,s))}function HM(e,t,n){bm(e,t,n)}function UM(e){let t=b(),n=L(),r=ic();es(r+1);let o=Is(n,r);if(e.dirty&&pp(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=xm(t,r);e.reset(i,ah),e.notifyOnChanges()}return!0}return!1}function zM(){return Em(b(),ic())}function Ry(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function GM(e){let t=Cp();return Qu(t,Y+e)}function WM(e,t=""){let n=b(),r=L(),o=e+Y,i=r.firstCreatePass?Vt(r,o,1,t,null):r.data[o],s=Fy(r,n,i,t,e);n[o]=s,Fr()&&Br(r,n,s,i),ut(i,!1)}var Fy=(e,t,n,r,o)=>(Pr(!0),Gh(t[F],r));function Py(e,t,n){let r=b(),o=Qm(r,e,t,n);return o!==fe&&fl(r,He(),o),Py}function ky(e,t,n,r,o){let i=b(),s=Km(i,e,t,n,r,o);return s!==fe&&fl(i,He(),s),ky}function Ly(e,t,n){xl(t)&&(t=t());let r=b(),o=Rr();if(Se(r,o,t)){let i=L(),s=rs();ol(i,s,r,e,t,r[F],n,!1)}return Ly}function qM(e,t){let n=xl(e);return n&&e.set(t),n}function jy(e,t){let n=b(),r=L(),o=$();return Hl(r,n,n[F],o,e,t),jy}function Vy(e,t,n){let r=L();if(r.firstCreatePass){let o=je(e);Oi(n,r.data,r.blueprint,o,!0),Oi(t,r.data,r.blueprint,o,!1)}}function Oi(e,t,n,r,o){if(e=H(e),Array.isArray(e))for(let i=0;i<e.length;i++)Oi(e[i],t,n,r,o);else{let i=L(),s=b(),a=$(),u=St(e)?e:H(e.provide),c=Vu(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(St(e)||!e.multi){let f=new rt(c,o,oe),h=Oo(u,t,o?l:l+p,d);h===-1?(qo(dr(a,s),i,u),Ao(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=Oo(u,t,l+p,d),h=Oo(u,t,l,l+p),y=f>=0&&n[f],N=h>=0&&n[h];if(o&&!N||!o&&!y){qo(dr(a,s),i,u);let C=Hy(o?$y:By,n.length,o,r,c);!o&&N&&(n[h].providerFactory=C),Ao(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(C),s.push(C)}else{let C=Ul(n[o?h:f],c,!o&&r);Ao(i,e,f>-1?f:h,C)}!o&&r&&N&&n[h].componentProviders++}}}function Ao(e,t,n,r){let o=St(t),i=tp(t);if(o||i){let u=(i?H(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(n);l===-1?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function Ul(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Oo(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function By(e,t,n,r){return Ri(this.multi,[])}function $y(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=ot(n,n[w],this.providerFactory.index,r);i=a.slice(0,s),Ri(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],Ri(o,i);return i}function Ri(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Hy(e,t,n,r,o){let i=new rt(e,n,oe);return i.multi=[],i.index=t,i.componentProviders=0,Ul(i,o,r&&!n),i}function ZM(e,t=[]){return n=>{n.providersResolver=(r,o)=>Vy(r,o?o(e):e,t)}}var Uy=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Pu(!1,n.type),o=r.length>0?jm([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=P({token:e,providedIn:"environment",factory:()=>new e(Z(Le))})}}return e})();function YM(e){cn("NgStandalone"),e.getStandaloneInjector=t=>t.get(Uy).getOrCreateStandaloneInjector(e)}function zy(e,t){let n=e[t];return n===fe?void 0:n}function Gy(e,t,n,r,o,i){let s=t+n;return Se(e,s,o)?$m(e,s+1,i?r.call(i,o):r(o)):zy(e,s+1)}function QM(e,t){let n=L(),r,o=e+Y;n.firstCreatePass?(r=Wy(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Ke(r.type,!0)),s,a=G(oe);try{let u=lr(!1),c=i();return lr(u),Ry(n,b(),o,c),c}finally{G(a)}}function Wy(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function KM(e,t,n){let r=e+Y,o=b(),i=Qu(o,r);return qy(o,r)?Gy(o,bp(),t,i.transform,n,i):i.transform(n)}function qy(e,t){return e[w].data[t].pure}function JM(e,t){return zr(e,t)}var XM=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Zy=new A("");function Gr(e){return!!e&&typeof e.then=="function"}function ws(e){return!!e&&typeof e.subscribe=="function"}var Yy=new A(""),zl=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=T(Yy,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=o();if(Gr(i))n.push(i);else if(ws(i)){let s=new Promise((a,u)=>{i.subscribe({complete:a,error:u})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Qy=new A("");function Ky(){Ea(()=>{throw new x(600,!1)})}function Jy(e){return e.isBoundToModule}var Xy=10;function ev(e,t,n){try{let r=n();return Gr(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Wr=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=T(ih),this.afterRenderManager=T(Zm),this.zonelessEnabled=T(Ds),this.dirtyFlags=0,this.deferredDirtyFlags=0,this.externalTestViews=new Set,this.beforeRender=new ee,this.afterTick=new ee,this.componentTypes=[],this.components=[],this.isStable=T(Lr).hasPendingTasks.pipe(J(n=>!n)),this._injector=T(Le)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}get injector(){return this._injector}bootstrap(n,r){let o=n instanceof yr;if(!this._injector.get(zl).done){let p=!o&&Zf(n),f=!1;throw new x(405,f)}let s;o?s=n:s=this._injector.get(Ft).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=Jy(s)?void 0:this._injector.get(Be),u=r||s.selector,c=s.create(nn.NULL,[],u,a),l=c.location.nativeElement,d=c.injector.get(Zy,null);return d?.registerApplication(l),c.onDestroy(()=>{this.detachView(c.hostView),Jn(this.components,c),d?.unregisterApplication(l)}),this._loadComponent(c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){if(this._runningTick)throw new x(101,!1);let n=M(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,M(n),this.afterTick.next()}}synchronize(){let n=null;this._injector.destroyed||(n=this._injector.get(vr,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let r=0;for(;this.dirtyFlags!==0&&r++<Xy;)this.synchronizeOnce(n)}synchronizeOnce(n){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8,this.beforeRender.next(r);for(let{_lView:o,notifyErrorHandler:i}of this._views)tv(o,i,r,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&7)return}else n?.begin?.(),n?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Ar(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Jn(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(Qy,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Jn(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new x(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Jn(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function tv(e,t,n,r){if(!n&&!Ar(e))return;yl(e,t,n&&!r?0:1)}var Fi=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},e0=(()=>{class e{compileModuleSync(n){return new Si(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Ou(n),i=Bc(o.declarations).reduce((s,a)=>{let u=Je(a);return u&&s.push(new rn(u)),s},[]);return new Fi(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var nv=(()=>{class e{constructor(){this.zone=T(q),this.changeDetectionScheduler=T(Rt),this.applicationRef=T(Wr)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),rv=new A("",{factory:()=>!1});function Gl({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new q(we(Ie({},Wl()),{scheduleInRootZone:n})),[{provide:q,useFactory:e},{provide:rr,multi:!0,useFactory:()=>{let r=T(nv,{optional:!0});return()=>r.initialize()}},{provide:rr,multi:!0,useFactory:()=>{let r=T(ov);return()=>{r.initialize()}}},t===!0?{provide:wl,useValue:!0}:[],{provide:El,useValue:n??Cc}]}function t0(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=Gl({ngZoneFactory:()=>{let o=Wl(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&cn("NgZone_CoalesceEvent"),new q(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Qf([{provide:rv,useValue:!0},{provide:Ds,useValue:!1},r])}function Wl(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var ov=(()=>{class e{constructor(){this.subscription=new k,this.initialized=!1,this.zone=T(q),this.pendingTasks=T(Lr)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{q.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{q.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var iv=(()=>{class e{constructor(){this.appRef=T(Wr),this.taskService=T(Lr),this.ngZone=T(q),this.zonelessEnabled=T(Ds),this.disableScheduling=T(wl,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new k,this.angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(pr):null,this.scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(T(El,{optional:!0})??!1),this.cancelScheduledCallback=null,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Jo||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 7:{this.appRef.deferredDirtyFlags|=8;break}case 9:case 8:case 6:case 10:default:this.appRef.dirtyFlags|=8}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?Ua:_c;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>r(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>r(()=>this.tick()))}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(pr+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ua(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function sv(){return typeof $localize<"u"&&$localize.locale||Cr}var Es=new A("",{providedIn:"root",factory:()=>T(Es,E.Optional|E.SkipSelf)||sv()});var Pi=new A("");function qn(e){return!e.moduleRef}function av(e){let t=qn(e)?e.r3Injector:e.moduleRef.injector,n=t.get(q);return n.run(()=>{qn(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ot,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),qn(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Pi);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Pi);s.add(i),e.moduleRef.onDestroy(()=>{Jn(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return ev(r,n,()=>{let i=t.get(zl);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(Es,Cr);if(xy(s||Cr),qn(e)){let a=t.get(Wr);return e.rootComponent!==void 0&&a.bootstrap(e.rootComponent),a}else return uv(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function uv(e,t){let n=e.injector.get(Wr);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new x(-403,!1);t.push(e)}var Xn=null;function cv(e=[],t){return nn.create({name:t,providers:[{provide:ju,useValue:"platform"},{provide:Pi,useValue:new Set([()=>Xn=null])},...e]})}function lv(e=[]){if(Xn)return Xn;let t=cv(e);return Xn=t,Ky(),dv(t),t}function dv(e){e.get(hh,null)?.forEach(n=>n())}function n0(){return!1}var Cs=(()=>{class e{static{this.__NG_ELEMENT_ID__=fv}}return e})();function fv(e){return pv($(),b(),(e&16)===16)}function pv(e,t,n){if(Tr(e)&&!n){let r=$e(e.index,t);return new it(r,r)}else if(e.type&175){let r=t[ne];return new it(r,t)}return null}var ki=class{constructor(){}supports(t){return Sl(t)}create(t){return new Li(t)}},hv=(e,t)=>t,Li=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||hv}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<cu(r,o,i)?n:r,a=cu(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let p=0;p<c;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;l<=h&&h<c&&(i[p]=f+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Sl(t))throw new x(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Vm(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new ji(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new br),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new br),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},ji=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Vi=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},br=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Vi,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function cu(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function lu(){return new bs([new ki])}var bs=(()=>{class e{static{this.\u0275prov=P({token:e,providedIn:"root",factory:lu})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||lu()),deps:[[e,new xf,new Mf]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new x(901,!1)}}return e})();function r0(e){try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=lv(r),i=[Gl({}),{provide:Rt,useExisting:iv},...n||[]],s=new wr({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return av({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}}function gv(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function mv(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function o0(e,t){cn("NgSignals");let n=Da(e);return t?.equal&&(n[_e].equal=t.equal),n}function _s(e){let t=M(null);try{return e()}finally{M(t)}}var Xl=null;function Ms(){return Xl}function T0(e){Xl??=e}var ql=class{};var ed=new A(""),td=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:()=>T(vv),providedIn:"platform"})}}return e})();var vv=(()=>{class e extends td{constructor(){super(),this._doc=T(ed),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Ms().getBaseHref(this._doc)}onPopState(n){let r=Ms().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Ms().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function nd(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function Zl(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function lt(e){return e&&e[0]!=="?"?"?"+e:e}var As=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=P({token:e,factory:()=>T(Iv),providedIn:"root"})}}return e})(),Dv=new A(""),Iv=(()=>{class e extends As{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??T(ed).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return nd(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+lt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+lt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+lt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(Z(td),Z(Dv,8))}}static{this.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var wv=(()=>{class e{constructor(n){this._subject=new ce,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=bv(Zl(Yl(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+lt(r))}normalize(n){return e.stripTrailingSlash(Cv(this._basePath,Yl(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+lt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+lt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=lt}static{this.joinWithSlash=nd}static{this.stripTrailingSlash=Zl}static{this.\u0275fac=function(r){return new(r||e)(Z(As))}}static{this.\u0275prov=P({token:e,factory:()=>Ev(),providedIn:"root"})}}return e})();function Ev(){return new wv(Z(As))}function Cv(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Yl(e){return e.replace(/\/index.html$/,"")}function bv(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}function N0(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var xs=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},A0=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new xs(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Ql(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Ql(i,o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(oe(ct),oe(st),oe(bs))}}static{this.\u0275dir=xr({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function Ql(e,t){e.context.$implicit=t.item}var O0=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new Ss,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Kl("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Kl("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(oe(ct),oe(st))}}static{this.\u0275dir=xr({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),Ss=class{constructor(){this.$implicit=null,this.ngIf=null}};function Kl(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${U(t)}'.`)}var R0=(()=>{class e{constructor(n){this._viewContainerRef=n,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static{this.\u0275fac=function(r){return new(r||e)(oe(ct))}}static{this.\u0275dir=xr({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[Yi]})}}return e})();function _v(e,t){return new x(2100,!1)}var Ts=class{createSubscription(t,n){return _s(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){_s(()=>t.unsubscribe())}},Ns=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},Mv=new Ns,xv=new Ts,F0=(()=>{class e{constructor(n){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(Gr(n))return Mv;if(ws(n))return xv;throw _v(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static{this.\u0275fac=function(r){return new(r||e)(oe(Cs,16))}}static{this.\u0275pipe=Tu({name:"async",type:e,pure:!1,standalone:!0})}}return e})();var P0=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Su({type:e})}static{this.\u0275inj=gu({})}}return e})(),Sv="browser",Tv="server";function k0(e){return e===Sv}function L0(e){return e===Tv}var Jl=class{};export{Ie as a,we as b,Nv as c,ad as d,k as e,dd as f,_ as g,to as h,no as i,ee as j,$t as k,wd as l,be as m,Ut as n,he as o,io as p,so as q,Ed as r,qe as s,J as t,Nd as u,Ze as v,Fn as w,Od as x,Rd as y,Fd as z,Ye as A,uo as B,Pd as C,aa as D,zt as E,co as F,kd as G,Ld as H,jd as I,ua as J,ca as K,$d as L,la as M,Hd as N,lo as O,Ud as P,zd as Q,Gd as R,Wd as S,qd as T,Zd as U,Yd as V,Qd as W,qs as X,Kd as Y,Jd as Z,x as _,pu as $,P as aa,gu as ba,lM as ca,A as da,E as ea,Z as fa,T as ga,dM as ha,rr as ia,Kt as ja,fM as ka,Su as la,xr as ma,Qf as na,ju as oa,Le as pa,pM as qa,hM as ra,Yi as sa,gM as ta,mM as ua,yM as va,vM as wa,DM as xa,Yp as ya,nn as za,bc as Aa,Lr as Ba,ce as Ca,q as Da,Ot as Ea,jt as Fa,IM as Ga,wM as Ha,hh as Ia,gh as Ja,EM as Ka,CM as La,Vr as Ma,wh as Na,bM as Oa,_M as Pa,MM as Qa,xM as Ra,SM as Sa,Rc as Ta,TM as Ua,Vc as Va,NM as Wa,hr as Xa,AM as Ya,oe as Za,OM as _a,Rt as $a,vr as ab,Cl as bb,ct as cb,cn as db,kM as eb,Am as fb,Lm as gb,Mi as hb,jm as ib,Gm as jb,Ym as kb,uy as lb,Ol as mb,cy as nb,LM as ob,jl as pb,Vl as qb,wy as rb,Bl as sb,$l as tb,by as ub,jM as vb,Ty as wb,VM as xb,BM as yb,$M as zb,HM as Ab,UM as Bb,zM as Cb,GM as Db,WM as Eb,Py as Fb,ky as Gb,Ly as Hb,qM as Ib,jy as Jb,ZM as Kb,YM as Lb,QM as Mb,KM as Nb,JM as Ob,XM as Pb,Gr as Qb,Qy as Rb,Wr as Sb,e0 as Tb,t0 as Ub,n0 as Vb,Cs as Wb,r0 as Xb,gv as Yb,mv as Zb,o0 as _b,_s as $b,Ms as ac,T0 as bc,ql as cc,ed as dc,wv as ec,N0 as fc,A0 as gc,O0 as hc,R0 as ic,F0 as jc,P0 as kc,Sv as lc,k0 as mc,L0 as nc,Jl as oc};
